package com.chatbot.theme

import androidx.compose.runtime.Immutable
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

@Immutable
data class ShadcnTheme(
    val background: Color,
    val foreground: Color,
    val card: Color,
    val cardForeground: Color,
    val popover: Color,
    val popoverForeground: Color,
    val primary: Color,
    val primaryForeground: Color,
    val secondary: Color,
    val secondaryForeground: Color,
    val muted: Color,
    val mutedForeground: Color,
    val accent: Color,
    val accentForeground: Color,
    val destructive: Color,
    val destructiveForeground: Color,
    val border: Color,
    val input: Color,
    val ring: Color,
    val chart1: Color,
    val chart2: Color,
    val chart3: Color,
    val chart4: Color,
    val chart5: Color,
    val sidebar: Color,
    val sidebarForeground: Color,
    val sidebarPrimary: Color,
    val sidebarPrimaryForeground: Color,
    val sidebarAccent: Color,
    val sidebarAccentForeground: Color,
    val sidebarBorder: Color,
    val sidebarRing: Color,
    val radiusSmall: Dp = 10.dp,
    val radiusMedium: Dp = 10.4.dp,
    val radiusLarge: Dp = 16.dp
)

val LocalShadcnTheme = staticCompositionLocalOf { 
    ShadcnTheme(
        background = Color.White,
        foreground = Color.Black,
        card = Color.White,
        cardForeground = Color.Black,
        popover = Color.White,
        popoverForeground = Color.Black,
        primary = Color.Black,
        primaryForeground = Color.White,
        secondary = Color.Gray,
        secondaryForeground = Color.Black,
        muted = Color.Gray,
        mutedForeground = Color.DarkGray,
        accent = Color.Gray,
        accentForeground = Color.Black,
        destructive = Color.Red,
        destructiveForeground = Color.White,
        border = Color.LightGray,
        input = Color.LightGray,
        ring = Color.Gray,
        chart1 = Color.Blue,
        chart2 = Color.Green,
        chart3 = Color.Yellow,
        chart4 = Color.Magenta,
        chart5 = Color.Cyan,
        sidebar = Color.White,
        sidebarForeground = Color.Black,
        sidebarPrimary = Color.Black,
        sidebarPrimaryForeground = Color.White,
        sidebarAccent = Color.Gray,
        sidebarAccentForeground = Color.Black,
        sidebarBorder = Color.LightGray,
        sidebarRing = Color.Gray
    )
}