package com.drna.shadcn.compose.component.chart

import androidx.compose.ui.graphics.Color

// Helper function to convert Compose Color to hex string for AndroidColor.parseColor
fun Color.toHexString(): String {
    return "#" + this.value.toString(16).substring(2, 8)
}

fun List<ChartEntry>.addEmptyStart(): List<ChartEntry> {
    if (this.isEmpty()) return this
    val emptyEntry = ChartEntry(0f, "")
    return listOf(emptyEntry) + this
}

fun List<GroupedChartEntry>.addGroupEmptyStart(): List<GroupedChartEntry> {
    if (isEmpty()) return this

    val emptyEntry = GroupedChartEntry("", List(first().values.size) { 0f })
    return listOf(emptyEntry) + this
}