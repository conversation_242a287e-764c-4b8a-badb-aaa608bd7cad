package com.drna.shadcn.compose.component.chart

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.github.mikephil.charting.charts.BarChart as MPBarChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.formatter.IndexAxisValueFormatter
import androidx.core.graphics.toColorInt
import com.drna.shadcn.compose.themes.shadcnColors
import com.github.mikephil.charting.data.BarData
import com.github.mikephil.charting.data.BarDataSet
import com.github.mikephil.charting.data.BarEntry

/**
 * A Jetpack Compose wrapper for MPAndroidChart's BarChart, styled to match Shadcn UI.
 *
 * @param modifier The modifier to be applied to the chart.
 * @param data A list of [ChartEntry] objects to display in the chart.
 * @param descriptionText The description text for the chart (usually hidden for clean UI).
 * @param barLabel The label for the bar dataset.
 * @param initialVisibleDataCount The initial number of data points to be visible on the chart.
 * @Param colors [BarChartStyle] that will be used to resolve the colors used for this chart in
 * @param contentSize [BarChartContentSize] that will be used to resolve the content size used for this chart in
 */
@Composable
fun BarChart(
    modifier: Modifier = Modifier,
    data: List<ChartEntry>,
    descriptionText: String = "",
    barLabel: String = "Values",
    initialVisibleDataCount: Int? = null,
    colors: BarChartStyle = BarChartDefaults.colors(),
    contentSize: BarChartContentSize = BarChartDefaults.contentSize()
) {
    val themeColors = MaterialTheme.shadcnColors

    AndroidView(
        modifier = modifier
            .fillMaxWidth()
            .height(300.dp)
            .padding(8.dp),
        factory = { ctx ->
            MPBarChart(ctx).apply {
                description.isEnabled = false
                legend.isEnabled = false
                setDrawGridBackground(false)

                // Enable horizontal scrolling/dragging and disable other gestures for a clean scroll experience
                isScaleXEnabled = true
                isScaleYEnabled = true
                setDragEnabled(true)
                setPinchZoom(true)
                isDoubleTapToZoomEnabled = true

                // X-axis configuration
                xAxis.apply {
                    position = XAxis.XAxisPosition.BOTTOM
                    setDrawGridLines(false)
                    setDrawAxisLine(true)
                    textColor = colors.xAxisTextColor.toHexString().toColorInt()
                    textSize = contentSize.xAxisTextSize
                    granularity = 1f
                    valueFormatter =
                        IndexAxisValueFormatter(data.map { it.label })
                }

                // Left Y-axis configuration
                axisLeft.apply {
                    setDrawGridLines(true)
                    gridColor = themeColors.border.toHexString().toColorInt()
                    setDrawAxisLine(true)
                    textColor = colors.yAxisTextColor.toHexString().toColorInt()
                    textSize = contentSize.yAxisTextSize
                    axisMinimum = 0f
                }

                axisRight.isEnabled = false

                // Animations
                animateY(800)
            }
        },
        update = { chart ->
            // Update chart data whenever Compose state changes
            val entries = data.mapIndexed { idx, value -> BarEntry(idx.toFloat(), value.y) }
            val dataSet = BarDataSet(entries, barLabel).also { dataSet ->
                // Convert Compose Color to Android Color Int
                val barColor = colors.barColor.toHexString().toColorInt()
                dataSet.colors = listOf(barColor) // Set bar color to Shadcn primary
                dataSet.valueTextColor = colors.dataTextColor.toHexString().toColorInt() // Value text color
                dataSet.valueTextSize = contentSize.dataTextSize // Value text size
                dataSet.setDrawValues(true) // Show value on top of bars
            }

            chart.data = BarData(dataSet).also {
                it.barWidth = contentSize.barWidth
            }

            // Set initial visible X-axis range
            initialVisibleDataCount?.also {
                if (data.size > it) {
                    // Set the maximum number of visible X-axis values (groups)
                    chart.setVisibleXRangeMaximum(it.toFloat())
                    chart.moveViewToX(-1f)
                } else {
                    // If data points are within the initialVisibleDataCount, fit all data to screen
                    chart.fitScreen()
                }
            }

            chart.xAxis.valueFormatter = IndexAxisValueFormatter(data.map { it.label }) // Update labels
            chart.description.text = descriptionText // Update description
            chart.invalidate() // Redraw the chart
        }
    )
}

data class BarChartStyle(
    val xAxisTextColor: Color,
    val yAxisTextColor: Color,
    val barColor: Color,
    val dataTextColor: Color,
)

data class BarChartContentSize(
    val barWidth: Float,
    val xAxisTextSize: Float,
    val yAxisTextSize: Float,
    val dataTextSize: Float
)

object BarChartDefaults {
    @Composable
    fun colors(): BarChartStyle {
        val colors = MaterialTheme.shadcnColors
        return BarChartStyle(
            xAxisTextColor = colors.foreground,
            yAxisTextColor = colors.foreground,
            barColor = colors.chart3,
            dataTextColor = colors.foreground,
        )
    }

    fun contentSize(): BarChartContentSize {
        return BarChartContentSize(
            barWidth = 0.6f,
            xAxisTextSize = 10f,
            yAxisTextSize = 10f,
            dataTextSize = 10f
        )
    }
}
