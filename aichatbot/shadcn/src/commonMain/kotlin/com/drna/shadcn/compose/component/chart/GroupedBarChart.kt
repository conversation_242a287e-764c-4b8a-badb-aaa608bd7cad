package com.drna.shadcn.compose.component.chart

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.graphics.toColorInt
import com.drna.shadcn.compose.themes.shadcnColors
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.data.BarData
import com.github.mikephil.charting.data.BarDataSet
import com.github.mikephil.charting.data.BarEntry
import com.github.mikephil.charting.formatter.IndexAxisValueFormatter
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet
import com.github.mikephil.charting.charts.BarChart as MP<PERSON><PERSON><PERSON><PERSON>

/**
 * A Jetpack Compose wrapper for MPAndroidChart's Bar<PERSON>hart, styled to match Shadcn UI.
 * Displays grouped datasets.
 *
 * @param modifier The modifier to be applied to the chart.
 * @param data A list of [GroupedChartEntry] objects, where each entry represents a group on the X-axis.
 * @param datasetLabels A list of labels for each dataset within a group. The size of this list
 * should match the `values` list size in [GroupedChartEntry].
 * @param descriptionText The description text for the chart (usually hidden for clean UI).
 *
 * Bar size calculation formula
 * (barWidth + barSpace) * number of bar in group + groupSpace = 1
 * (0.25 + 0.05) * 3 + 0.1 = 1.00 --> interval per "group"
 * ref: https://github.com/PhilJay/MPAndroidChart/blob/9c7275a0596a7ac0e50ca566e680f7f9d73607af/MPChartExample/src/main/java/com/xxmassdeveloper/mpchartexample/CombinedChartActivity.java#L162
 */
@Composable
fun GroupedBarChart(
    modifier: Modifier = Modifier,
    data: List<GroupedChartEntry>,
    datasetLabels: List<String>,
    descriptionText: String = "",
    initialVisibleDataCount: Int = 3,
    colors: GroupedBarChartStyle = GroupedBarChartDefaults.colors(),
    contentSize: GroupedBarChartContentSize = GroupedBarChartDefaults.contentSize()
) {
    val themeColors = MaterialTheme.shadcnColors

    // Define a set of colors for the different datasets in the group
    val chartColors = colors.chartColors

    AndroidView(
        modifier = modifier
            .fillMaxWidth()
            .height(300.dp)
            .padding(8.dp),
        factory = { ctx ->
            MPBarChart(ctx).apply {
                description.isEnabled = false
                legend.isEnabled = true
                setDrawGridBackground(false)

                // Enable horizontal scrolling/dragging and disable other gestures for a clean scroll experience
                isScaleXEnabled = true
                isScaleYEnabled = true
                setDragEnabled(true)
                setPinchZoom(true)
                isDoubleTapToZoomEnabled = true

                // X-axis configuration
                xAxis.apply {
                    position = XAxis.XAxisPosition.BOTTOM
                    setDrawGridLines(false)
                    setDrawAxisLine(true)
                    textColor = colors.xAxisTextColor.toHexString().toColorInt()
                    textSize = contentSize.xAxisTextSize
                    granularity = 1f
                    isGranularityEnabled = true // Important for grouping
                    valueFormatter = IndexAxisValueFormatter(data.map { it.label })
                    setCenterAxisLabels(true)
                }

                // Left Y-axis configuration
                axisLeft.apply {
                    setDrawGridLines(true)
                    gridColor = themeColors.border.toHexString().toColorInt()
                    setDrawAxisLine(true)
                    textColor = colors.yAxisTextColor.toHexString().toColorInt()
                    textSize = contentSize.yAxisTextSize
                    axisMinimum = 0f
                }

                axisRight.isEnabled = false
                animateY(800)
            }
        },
        update = { chart ->
            val dataSets = mutableListOf<IBarDataSet>()
            val numDatasets = datasetLabels.size
            val numGroups = data.size

            // Create a BarDataSet for each series
            for (i in 0 until numDatasets) {
                val entries = data.mapIndexed { index, groupedEntry ->
                    BarEntry(index.toFloat(), groupedEntry.values[i])
                }
                val dataSet = BarDataSet(entries, datasetLabels[i]).apply {
                    color = chartColors[i % chartColors.size] // Assign color from our palette
                    valueTextColor = colors.dataTextColor.toHexString().toColorInt()
                    valueTextSize = contentSize.dataTextSize
                    setDrawValues(true)
                }
                dataSets.add(dataSet)
            }

            val barData = BarData(dataSets).also {
                it.barWidth = contentSize.barWidth
            }

            chart.data = barData

            // Set X-axis properties after grouping to ensure correct label display
            val barGroupWidth = barData.getGroupWidth(contentSize.groupSpace,contentSize. barSpace)
            chart.xAxis.axisMinimum = contentSize.groupSpace / 2f
            chart.xAxis.axisMaximum = chart.xAxis.axisMinimum + barGroupWidth * numGroups
            chart.xAxis.labelCount = numGroups // Ensure all labels are counted

            chart.barData.groupBars(0f, contentSize.groupSpace, contentSize.barSpace) // Group the bars starting from x = 0

            // Set initial visible X-axis range
            if (numGroups > initialVisibleDataCount) {
                // Set the maximum number of visible X-axis values (groups)
                chart.setVisibleXRangeMaximum(initialVisibleDataCount.toFloat())
                // Move the view to the start (x=0) to show the first points
                chart.moveViewToX(0f)
            } else {
                // If data points are within the initialVisibleDataCount, fit all data to screen
                chart.fitScreen()
            }

            chart.description.text = descriptionText
            chart.invalidate() // Redraw the chart
        }
    )
}

data class GroupedBarChartStyle(
    val xAxisTextColor: Color,
    val yAxisTextColor: Color,
    val chartColors: List<Int>,
    val dataTextColor: Color,
)

data class GroupedBarChartContentSize(
    val barWidth: Float,
    val groupSpace: Float,
    val barSpace: Float,
    val xAxisTextSize: Float,
    val yAxisTextSize: Float,
    val dataTextSize: Float
)

object GroupedBarChartDefaults {
    @Composable
    fun colors(): GroupedBarChartStyle {
        val colors = MaterialTheme.shadcnColors
        return GroupedBarChartStyle(
            xAxisTextColor = colors.foreground,
            yAxisTextColor = colors.foreground,
            chartColors = listOf(
                colors.chart1.toHexString().toColorInt(),
                colors.chart2.toHexString().toColorInt(),
                colors.chart3.toHexString().toColorInt(),
                colors.chart4.toHexString().toColorInt(),
                colors.chart5.toHexString().toColorInt(),
            ),
            dataTextColor = colors.foreground,
        )
    }

    fun contentSize(): GroupedBarChartContentSize {
        return GroupedBarChartContentSize(
            barWidth = 0.25f,
            groupSpace = 0.1f,
            barSpace = 0.05f,
            xAxisTextSize = 10f,
            yAxisTextSize = 10f,
            dataTextSize = 10f
        )
    }
}