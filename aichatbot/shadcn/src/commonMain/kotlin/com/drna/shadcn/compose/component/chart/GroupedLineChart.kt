package com.drna.shadcn.compose.component.chart

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.graphics.toColorInt
import com.drna.shadcn.compose.themes.shadcnColors
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.AxisBase
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.ValueFormatter
import kotlin.Boolean

/**
 * A Jetpack Compose wrapper for MPAndroidChart's LineChart, styled to match Shadcn UI,
 * configured to display multiple overlaid line charts.
 *
 * @param modifier The modifier to be applied to the chart.
 * @param data A list of [GroupedChartEntry] objects, where each entry represents a group on the X-axis.
 * @param datasetLabels A list of labels for each dataset. The size of this list should match
 * the number of values in [GroupedChartEntry.values].
 * @param descriptionText The description text for the chart (usually hidden for clean UI).
 * @param initialVisibleDataCount The number of data groups to display initially.
 * @param drawCircles Whether to draw circles at data points.
 * @param drawValues Whether to draw values on top of the lines.
 * @param lineChartVariant The variant of the line chart (either Line or Area).
 * @param colors [GroupedLineChartStyle] that will be used to resolve the colors used for this chart in
 * @param contentSize [GroupedLineChartContentSize] that will be used to resolve the content size used for this chart in
 */
@Composable
fun GroupedLineChart(
    modifier: Modifier = Modifier,
    data: List<GroupedChartEntry>,
    datasetLabels: List<String>,
    descriptionText: String = "",
    initialVisibleDataCount: Int = 5,
    drawCircles: Boolean = true,
    drawValues: Boolean = true,
    lineChartVariant: LineChartVariant = LineChartVariant.Line,
    colors: GroupedLineChartStyle = GroupedChartDefaults.colors(),
    contentSize: GroupedLineChartContentSize = GroupedChartDefaults.contentSize()
) {
    val themeColors = MaterialTheme.shadcnColors
    val chartColors = colors.chartColors

    AndroidView(
        modifier = modifier
            .fillMaxWidth()
            .height(300.dp)
            .padding(8.dp),
        factory = { ctx ->
            LineChart(ctx).apply {
                description.isEnabled = false
                legend.isEnabled = true
                setDrawGridBackground(false)

                // Enable horizontal scrolling/dragging
                isScaleXEnabled = true
                isScaleYEnabled = true
                setDragEnabled(true)
                setPinchZoom(true)
                isDoubleTapToZoomEnabled = true

                // X-axis configuration
                xAxis.apply {
                    position = XAxis.XAxisPosition.BOTTOM
                    setDrawGridLines(false)
                    setDrawAxisLine(true)
                    textColor = colors.xAxisTextColor.toHexString().toColorInt()
                    textSize = contentSize.xAxisTextSize
                    granularity = 1f
                    isGranularityEnabled = true
                    valueFormatter = object : ValueFormatter() {
                        override fun getAxisLabel(
                            value: Float,
                            axis: AxisBase?
                        ): String? {
                            return data.getOrNull(value.toInt())?.label
                        }
                    }
                }

                // Left Y-axis configuration
                axisLeft.apply {
                    setDrawGridLines(true)
                    gridColor = themeColors.border.toHexString().toColorInt()
                    setDrawAxisLine(true)
                    textColor = colors.yAxisTextColor.toHexString().toColorInt()
                    textSize = contentSize.yAxisTextSize
                    axisMinimum = 0f
                }

                axisRight.isEnabled = false
                animateY(800)
            }
        },
        update = { chart ->
            val numGroups = data.size
            val dataSets = datasetLabels.mapIndexed { i, label ->
                val entries = data.mapIndexed { groupIndex, groupedEntry ->
                    Entry(groupIndex.toFloat(), groupedEntry.values[i])
                }

                LineDataSet(entries, label).also {
                    it.color = chartColors[i % chartColors.size] // Line color
                    it.mode = LineDataSet.Mode.CUBIC_BEZIER
                    it.lineWidth = contentSize.lineWidth
                    it.cubicIntensity = 0.2f
                    it.setDrawCircles(drawCircles) // Use new parameter
                    it.circleRadius = contentSize.circleRadius // Default circle radius
                    it.setCircleColor(chartColors[i % chartColors.size]) // Circle color
                    it.setDrawValues(drawValues) // Use new parameter
                    it.valueTextColor = colors.dataTextColor.toHexString().toColorInt() // Value text color
                    it.valueTextSize = contentSize.dataTextSize // Value text size
                    if (lineChartVariant == LineChartVariant.Area) {
                        it.setDrawFilled(true) // Enable filled area
                        it.fillColor = chartColors[i % chartColors.size] // Fill color
                        it.fillAlpha = 120
                    }
                }
            }

            chart.data = LineData(dataSets)
            chart.xAxis.labelCount = numGroups

            // Set initial visible X-axis range
            if (data.size > initialVisibleDataCount) {
                chart.setVisibleXRangeMaximum(initialVisibleDataCount.toFloat())
                chart.moveViewToX(0f)
            } else {
                chart.fitScreen()
            }

            chart.description.text = descriptionText
            chart.invalidate()
        }
    )
}

data class GroupedLineChartStyle(
    val xAxisTextColor: Color,
    val yAxisTextColor: Color,
    val dataTextColor: Color,
    val chartColors: List<Int>,
    val circleColor: Color,
)

data class GroupedLineChartContentSize(
    val lineWidth: Float,
    val xAxisTextSize: Float,
    val yAxisTextSize: Float,
    val circleRadius: Float,
    val dataTextSize: Float
)

object GroupedChartDefaults {
    @Composable
    fun colors(): GroupedLineChartStyle {
        val colors = MaterialTheme.shadcnColors
        return GroupedLineChartStyle(
            xAxisTextColor = colors.foreground,
            yAxisTextColor = colors.foreground,
            dataTextColor = colors.foreground,
            chartColors = listOf(
                colors.chart1.toHexString().toColorInt(),
                colors.chart2.toHexString().toColorInt(),
                colors.chart3.toHexString().toColorInt(),
                colors.chart4.toHexString().toColorInt(),
                colors.chart5.toHexString().toColorInt(),
            ),
            circleColor = colors.chart3,
        )
    }

    fun contentSize(): GroupedLineChartContentSize {
        return GroupedLineChartContentSize(
            lineWidth = 2f,
            xAxisTextSize = 10f,
            yAxisTextSize = 10f,
            circleRadius = 4f,
            dataTextSize = 9f
        )
    }
}