package com.drna.shadcn.compose.component

import android.graphics.BlurMaskFilter
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ProvideTextStyle
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.drna.shadcn.compose.themes.Shadows
import com.drna.shadcn.compose.themes.radius
import com.drna.shadcn.compose.themes.shadcnColors

data class BoxShadow(
    val offsetX: Dp = 0.dp,
    val offsetY: Dp = 0.dp,
    val blurRadius: Dp = 0.dp,
    val spread: Dp = 0f.dp,
    val color: Color = Color.Gray
)

/**
 * A Jetpack Compose Card component inspired by Shadcn UI.
 * This is the main container for a card.
 *
 * @param modifier The modifier to be applied to the card container.
 * @param content The composable content of the card.
 */
@Composable
fun Card(
    modifier: Modifier = Modifier,
    shadow: BoxShadow = Shadows.shadow,
    radius: Dp = MaterialTheme.radius.lg,
    background: Color? = null,
    border: Color? = null,
    content: @Composable ColumnScope.() -> Unit
) {
    val applyBg = background ?: MaterialTheme.shadcnColors.card
    val applyBorder = border ?: MaterialTheme.shadcnColors.border
    Box(
        modifier = modifier
            .drawBehind {
                drawIntoCanvas {
                    val paint = Paint()
                    val frameworkPaint = paint.asFrameworkPaint()
                    val spreadPixel = shadow.spread.toPx()
                    val leftPixel = (0f - spreadPixel) + shadow.offsetX.toPx()
                    val topPixel = (0f - spreadPixel) + shadow.offsetY.toPx()
                    val rightPixel = (size.width + spreadPixel)
                    val bottomPixel = (size.height + spreadPixel)

                    if (shadow.blurRadius != 0.dp) {
                        frameworkPaint.maskFilter =
                            BlurMaskFilter(shadow.blurRadius.toPx(), BlurMaskFilter.Blur.NORMAL)
                    }

                    frameworkPaint.color = shadow.color.toArgb()
                    it.drawRoundRect(
                        left = leftPixel,
                        top = topPixel,
                        right = rightPixel,
                        bottom = bottomPixel,
                        radiusX = radius.toPx(),
                        radiusY = radius.toPx(),
                        paint
                    )
                }
            }
            .background(applyBg, RoundedCornerShape(radius))
            .border(1.dp, applyBorder,RoundedCornerShape(radius))
    ) {
        Column(content = content)
    }
}

/**
 * Composable for the header section of a ShadcnCard.
 *
 * @param modifier The modifier to be applied to the header.
 * @param content The composable content of the header.
 */
@Composable
fun CardHeader(
    modifier: Modifier = Modifier,
    content: @Composable ColumnScope.() -> Unit
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        content = content
    )
}

/**
 * Composable for the title of a Card.
 * This should be used within [CardHeader].
 *
 * @param modifier The modifier to be applied to the title text.
 * @param content The composable content of the title.
 */
@Composable
fun CardTitle(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    val colors = MaterialTheme.shadcnColors
    ProvideTextStyle(
        value = TextStyle(
            color = colors.cardForeground,
            fontWeight = FontWeight.SemiBold,
            fontSize = 18.sp
        )
    ) {
        content()
    }
}

/**
 * Composable for the description of a ShadcnCard.
 * This should be used within [CardHeader].
 *
 * @param modifier The modifier to be applied to the description text.
 * @param content The composable content of the description.
 */
@Composable
fun CardDescription(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    val colors = MaterialTheme.shadcnColors
    ProvideTextStyle(
        value = TextStyle(
            color = colors.mutedForeground,
            fontSize = 14.sp
        )
    ) {
        Column(modifier = modifier) {
            content()
        }
    }
}

/**
 * Composable for the main content area of a Card.
 *
 * @param modifier The modifier to be applied to the content area.
 * @param content The composable content of the main area.
 */
@Composable
fun CardContent(
    modifier: Modifier = Modifier,
    content: @Composable ColumnScope.() -> Unit
) {
    val colors = MaterialTheme.shadcnColors
    ProvideTextStyle(
        value = TextStyle(
            color = colors.foreground,
        )
    ) {
        Column(
            modifier = modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            content = content
        )
    }
}

/**
 * Composable for the footer section of a ShadcnCard.
 *
 * @param modifier The modifier to be applied to the footer.
 * @param content The composable content of the footer.
 */
@Composable
fun CardFooter(
    modifier: Modifier = Modifier,
    content: @Composable ColumnScope.() -> Unit
) {
    val colors = MaterialTheme.shadcnColors
    ProvideTextStyle(
        value = TextStyle(
            color = colors.foreground,
        )
    ) {
        Column(
            modifier = modifier
                .fillMaxWidth()
                .padding(16.dp),
            content = content
        )
    }
}

object CardDefaults {
    fun shadows(elevation: Dp = 2.dp): BoxShadow {
        val offsetY = elevation * 0.5f
        val blurRadius = elevation * 0.75f
        val spread = elevation * 0.25f
        val shadowAlpha = (0.2f + (elevation.value / 20f)).coerceIn(0f, 0.6f)

        return BoxShadow(
            offsetX = 0.dp,
            offsetY = offsetY,
            blurRadius = blurRadius,
            spread = spread,
            color = Color.Gray.copy(alpha = shadowAlpha)
        )
    }

    fun shadows(boxShadow: BoxShadow): BoxShadow {
        return boxShadow
    }

}