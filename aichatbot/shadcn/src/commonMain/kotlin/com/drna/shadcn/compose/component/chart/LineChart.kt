package com.drna.shadcn.compose.component.chart

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.graphics.toColorInt
import com.drna.shadcn.compose.themes.shadcnColors
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.IndexAxisValueFormatter

enum class LineChartVariant {
    Line,
    Area
}

/**
 * A Jetpack Compose wrapper for MPAndroid<PERSON>hart's LineChart, styled to match Shadcn UI,
 * configured to display a single line chart.
 *
 * @param modifier The modifier to be applied to the chart.
 * @param data A list of [ChartEntry] objects to display in the chart.
 * @param descriptionText The description text for the chart (usually hidden for clean UI).
 * @param areaLabel The label for the area dataset.
 * @param initialVisibleDataCount The number of data points to display initially. If the total
 *  number of data points exceeds this, the chart will be horizontally scrollable.
 * @param drawCircles Whether to draw circles at data points.
 * @param drawValues Whether to draw values on top of the line.
 * @param lineChartVariant The variant of the line chart (either Line or Area).
 * @param colors [LineChartStyle] that will be used to resolve the colors used for this chart in
 * @param contentSize [LineChartContentSize] that will be used to resolve the content size used for this chart in
 */
@Composable
fun LineChart(
    modifier: Modifier = Modifier,
    data: List<ChartEntry>,
    descriptionText: String = "",
    areaLabel: String = "Values",
    initialVisibleDataCount: Int? = null,
    drawCircles: Boolean = true,
    drawValues: Boolean = true,
    lineChartVariant: LineChartVariant = LineChartVariant.Line,
    colors: LineChartStyle = LineChartDefaults.colors(),
    contentSize: LineChartContentSize = LineChartDefaults.contentSize()
) {
    val themeColors = MaterialTheme.shadcnColors

    AndroidView(
        modifier = modifier
            .fillMaxWidth()
            .height(300.dp)
            .padding(8.dp),
        factory = { ctx ->
            LineChart(ctx).apply {
                description.isEnabled = false
                legend.isEnabled = false
                setDrawGridBackground(false)

                // Enable horizontal scrolling/dragging
                isScaleXEnabled = true
                isScaleYEnabled = false
                setDragEnabled(true)
                setPinchZoom(false)
                isDoubleTapToZoomEnabled = false

                // X-axis configuration
                xAxis.apply {
                    position = XAxis.XAxisPosition.BOTTOM
                    setDrawGridLines(false)
                    setDrawAxisLine(true)
                    textColor = colors.xAxisTextColor.toHexString().toColorInt()
                    textSize = contentSize.xAxisTextSize
                    granularity = 1f
                    valueFormatter = IndexAxisValueFormatter(data.map { it.label })
                }

                // Left Y-axis configuration
                axisLeft.apply {
                    setDrawGridLines(true)
                    gridColor = themeColors.border.toHexString().toColorInt()
                    setDrawAxisLine(false)
                    textColor = colors.yAxisTextColor.toHexString().toColorInt()
                    textSize = contentSize.yAxisTextSize
                    axisMinimum = 0f
                }

                axisRight.isEnabled = false
                animateY(800)
            }
        },
        update = { chart ->
            val entries = data.mapIndexed { idx, value -> Entry(idx.toFloat(), value.y) }
            val dataSet = LineDataSet(entries, areaLabel).also {
                it.color = colors.lineColor.toHexString().toColorInt() // Line color
                it.mode = LineDataSet.Mode.CUBIC_BEZIER // Smooth curves
                it.setDrawCircles(false) // Hide data points
                it.setDrawValues(false) // Hide value labels on points
                it.lineWidth = contentSize.lineWidth // Line thickness
                it.setDrawCircles(drawCircles) // Use new parameter
                it.circleRadius = contentSize.circleRadius // Default circle radius
                it.setCircleColor(colors.circleColor.toHexString().toColorInt()) // Circle color
                it.setDrawValues(drawValues) // Use new parameter
                it.valueTextColor = colors.dataTextColor.toHexString().toColorInt() // Value text color
                it.valueTextSize = contentSize.dataTextSize // Value text size

                if (lineChartVariant == LineChartVariant.Area) {
                    it.setDrawFilled(true) // Enable filled area
                    it.fillColor = colors.areaColor.toHexString().toColorInt() // Fill color
                    it.fillAlpha = 180 // Set fill transparency
                }
            }

            chart.data = LineData(dataSet)
            chart.xAxis.valueFormatter = IndexAxisValueFormatter(data.map { it.label })
            chart.description.text = descriptionText

            // Set initial visible X-axis range
            initialVisibleDataCount?.also {
                if (data.size > it) {
                    chart.setVisibleXRangeMaximum(it.toFloat())
                    chart.moveViewToX(0f)
                } else {
                    chart.fitScreen()
                }
            }

            chart.invalidate()
        }
    )
}

data class LineChartStyle(
    val xAxisTextColor: Color,
    val yAxisTextColor: Color,
    val dataTextColor: Color,
    val lineColor: Color,
    val circleColor: Color,
    val areaColor: Color
)

data class LineChartContentSize(
    val lineWidth: Float,
    val xAxisTextSize: Float,
    val yAxisTextSize: Float,
    val circleRadius: Float,
    val dataTextSize: Float
)

object LineChartDefaults {
    @Composable
    fun colors(): LineChartStyle {
        val colors = MaterialTheme.shadcnColors
        return LineChartStyle(
            xAxisTextColor = colors.foreground,
            yAxisTextColor = colors.foreground,
            dataTextColor = colors.foreground,
            lineColor = colors.chart3,
            circleColor = colors.chart3,
            areaColor = colors.chart2
        )
    }

    fun contentSize(): LineChartContentSize {
        return LineChartContentSize(
            lineWidth = 2f,
            xAxisTextSize = 10f,
            yAxisTextSize = 10f,
            circleRadius = 4f,
            dataTextSize = 9f
        )
    }
}