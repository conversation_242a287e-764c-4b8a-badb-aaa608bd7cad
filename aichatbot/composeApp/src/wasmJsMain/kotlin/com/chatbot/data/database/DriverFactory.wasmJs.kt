package com.chatbot.data.database

import app.cash.sqldelight.db.SqlDriver
import app.cash.sqldelight.driver.worker.WebWorkerDriver
import org.w3c.dom.Worker

actual class DriverFactory {
    actual fun createDriver(): SqlDriver {
        return WebWorkerDriver(
            Worker(
                js("""new URL("@cashapp/sqldelight-sqljs/sqljs.worker.js", import.meta.url)""")
            )
        ).also { 
            AppDatabase.Schema.create(it)
        }
    }
}