package com.chatbot

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import com.chatbot.di.appModule
import com.chatbot.di.platformModule
import com.chatbot.theme.LocalShadcnTheme
import com.chatbot.theme.DefaultTheme
import com.chatbot.theme.ShadcnTheme
import com.chatbot.ui.pages.ProviderSettingsPage
import com.drna.shadcn.compose.themes.ShadcnTheme
import org.koin.compose.KoinApplication

@Composable
fun App() {
    KoinApplication(
        application = {
            modules(platformModule(), appModule())
        }
    ) {
            ShadcnTheme {
                ProviderSettingsPage()
            }
    }
}