package com.chatbot.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.chatbot.data.models.LlmModel
import com.chatbot.data.models.LlmProviderModel
import com.chatbot.data.services.ApiConnectionService
import com.chatbot.data.services.ConnectionTestResult
import com.chatbot.data.services.LlmDataService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 应用数据管理器 - ViewModel实现
 */
class LlmProvider(
    private val dataService: LlmDataService
) : ViewModel() {
    
    private val _providers = MutableStateFlow<List<LlmProviderModel>>(emptyList())
    val providers: StateFlow<List<LlmProviderModel>> = _providers.asStateFlow()
    
    private val _models = MutableStateFlow<List<LlmModel>>(emptyList())
    val models: StateFlow<List<LlmModel>> = _models.asStateFlow()
    
    private val _selectedProvider = MutableStateFlow<LlmProviderModel?>(null)
    val selectedProvider: StateFlow<LlmProviderModel?> = _selectedProvider.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    private val apiConnectionService = ApiConnectionService()
    
    // ========== 数据获取（按需加载）==========
    
    /**
     * 加载providers数据（按需）
     */
    fun loadProviders() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val providerList = dataService.getAllProviders()
                _providers.value = providerList
                
                // 如果还没有选中的provider，选择第一个
                if (_selectedProvider.value == null && providerList.isNotEmpty()) {
                    selectProvider(providerList.first())
                }
            } catch (e: Exception) {
                _error.value = "Failed to load providers: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 加载models数据（按需）
     */
    fun loadModels() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                _models.value = dataService.getAllModels()
            } catch (e: Exception) {
                _error.value = "Failed to load models: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 选择Provider
     */
    fun selectProvider(provider: LlmProviderModel) {
        _selectedProvider.value = provider
        // 加载该Provider的模型
        viewModelScope.launch {
            provider.id?.let { providerId ->
                val providerModels = dataService.getModelsByProviderId(providerId)
                _models.value = providerModels
            }
        }
    }
    
    // ========== Provider CRUD操作 ==========
    
    /**
     * 创建提供商
     */
    fun createProvider(provider: LlmProviderModel, onSuccess: (LlmProviderModel) -> Unit = {}, onError: (String) -> Unit = {}) {
        viewModelScope.launch {
            try {
                val created = dataService.saveProvider(provider)
                if (created != null) {
                    loadProviders() // 重新加载列表
                    onSuccess(created)
                } else {
                    onError("Failed to create provider")
                }
            } catch (e: Exception) {
                onError("Error creating provider: ${e.message}")
            }
        }
    }
    
    /**
     * 更新提供商
     */
    fun updateProvider(provider: LlmProviderModel, onSuccess: () -> Unit = {}, onError: (String) -> Unit = {}) {
        viewModelScope.launch {
            try {
                val updated = dataService.saveProvider(provider)
                if (updated != null) {
                    loadProviders() // 重新加载列表
                    if (_selectedProvider.value?.id == provider.id) {
                        _selectedProvider.value = updated
                    }
                    onSuccess()
                } else {
                    onError("Failed to update provider")
                }
            } catch (e: Exception) {
                onError("Error updating provider: ${e.message}")
            }
        }
    }
    
    /**
     * 删除提供商
     */
    fun deleteProvider(providerId: Int, onSuccess: () -> Unit = {}, onError: (String) -> Unit = {}) {
        viewModelScope.launch {
            try {
                val deleted = dataService.deleteProvider(providerId)
                if (deleted) {
                    loadProviders() // 重新加载列表
                    if (_selectedProvider.value?.id == providerId) {
                        _selectedProvider.value = null
                    }
                    onSuccess()
                } else {
                    onError("Failed to delete provider")
                }
            } catch (e: Exception) {
                onError("Error deleting provider: ${e.message}")
            }
        }
    }
    
    /**
     * 切换提供商状态
     */
    fun toggleProviderStatus(provider: LlmProviderModel, onSuccess: () -> Unit = {}, onError: (String) -> Unit = {}) {
        viewModelScope.launch {
            try {
                val toggled = dataService.toggleProviderStatus(provider)
                if (toggled) {
                    loadProviders() // 重新加载列表
                    onSuccess()
                } else {
                    onError("Failed to toggle provider status")
                }
            } catch (e: Exception) {
                onError("Error toggling provider status: ${e.message}")
            }
        }
    }
    
    // ========== Model CRUD操作 ==========
    
    /**
     * 创建模型
     */
    fun createModel(model: LlmModel, onSuccess: (LlmModel) -> Unit = {}, onError: (String) -> Unit = {}) {
        viewModelScope.launch {
            try {
                val created = dataService.saveModel(model)
                if (created != null) {
                    // 如果是当前选中provider的模型，刷新模型列表
                    if (_selectedProvider.value?.id == model.providerId) {
                        _selectedProvider.value?.id?.let { providerId ->
                            _models.value = dataService.getModelsByProviderId(providerId)
                        }
                    }
                    onSuccess(created)
                } else {
                    onError("Failed to create model")
                }
            } catch (e: Exception) {
                onError("Error creating model: ${e.message}")
            }
        }
    }
    
    /**
     * 更新模型
     */
    fun updateModel(model: LlmModel, onSuccess: () -> Unit = {}, onError: (String) -> Unit = {}) {
        viewModelScope.launch {
            try {
                val updated = dataService.saveModel(model)
                if (updated != null) {
                    // 如果是当前选中provider的模型，刷新模型列表
                    if (_selectedProvider.value?.id == model.providerId) {
                        _selectedProvider.value?.id?.let { providerId ->
                            _models.value = dataService.getModelsByProviderId(providerId)
                        }
                    }
                    onSuccess()
                } else {
                    onError("Failed to update model")
                }
            } catch (e: Exception) {
                onError("Error updating model: ${e.message}")
            }
        }
    }
    
    /**
     * 删除模型
     */
    fun deleteModel(modelId: Int, onSuccess: () -> Unit = {}, onError: (String) -> Unit = {}) {
        viewModelScope.launch {
            try {
                val deleted = dataService.deleteModel(modelId)
                if (deleted) {
                    // 刷新当前provider的模型列表
                    _selectedProvider.value?.id?.let { providerId ->
                        _models.value = dataService.getModelsByProviderId(providerId)
                    }
                    onSuccess()
                } else {
                    onError("Failed to delete model")
                }
            } catch (e: Exception) {
                onError("Error deleting model: ${e.message}")
            }
        }
    }
    
    /**
     * 切换模型状态
     */
    fun toggleModelStatus(model: LlmModel, onSuccess: () -> Unit = {}, onError: (String) -> Unit = {}) {
        viewModelScope.launch {
            try {
                val toggled = dataService.toggleModelStatus(model)
                if (toggled) {
                    // 刷新当前provider的模型列表
                    _selectedProvider.value?.id?.let { providerId ->
                        _models.value = dataService.getModelsByProviderId(providerId)
                    }
                    onSuccess()
                } else {
                    onError("Failed to toggle model status")
                }
            } catch (e: Exception) {
                onError("Error toggling model status: ${e.message}")
            }
        }
    }
    
    /**
     * 批量导入模型
     */
    fun importModels(providerId: Int, models: List<LlmModel>, onSuccess: (List<LlmModel>) -> Unit = {}, onError: (String) -> Unit = {}) {
        viewModelScope.launch {
            try {
                val imported = dataService.importModels(providerId, models)
                // 刷新当前provider的模型列表
                if (_selectedProvider.value?.id == providerId) {
                    _models.value = dataService.getModelsByProviderId(providerId)
                }
                onSuccess(imported)
            } catch (e: Exception) {
                onError("Error importing models: ${e.message}")
            }
        }
    }
    
    // ========== API测试 ==========
    
    /**
     * 测试API连接
     */
    fun testConnection(provider: LlmProviderModel, onResult: (ConnectionTestResult) -> Unit) {
        viewModelScope.launch {
            try {
                val result = apiConnectionService.testConnection(provider)
                onResult(result)
            } catch (e: Exception) {
                onResult(ConnectionTestResult(
                    success = false,
                    message = "Connection test failed: ${e.message}"
                ))
            }
        }
    }
    
    /**
     * 清除错误
     */
    fun clearError() {
        _error.value = null
    }
    
    override fun onCleared() {
        super.onCleared()
        apiConnectionService.close()
    }
}