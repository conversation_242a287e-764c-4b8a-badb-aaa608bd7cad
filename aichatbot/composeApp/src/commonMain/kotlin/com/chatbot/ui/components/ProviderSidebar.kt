package com.chatbot.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.chatbot.data.models.LlmProviderModel
import com.chatbot.theme.LocalShadcnTheme

/**
 * 提供商侧边栏组件
 *
 * 提供：
 * - 搜索功能
 * - 提供商列表
 * - 添加提供商按钮
 */
@Composable
fun ProviderSidebar(
    providers: List<LlmProviderModel>,
    selectedProvider: LlmProviderModel?,
    onProviderSelected: (LlmProviderModel) -> Unit,
    onToggleProvider: (LlmProviderModel) -> Unit,
    onAddProvider: () -> Unit,
    modifier: Modifier = Modifier
) {
    val theme = LocalShadcnTheme.current
    var searchQuery by remember { mutableStateOf("") }
    
    // 过滤providers
    val filteredProviders = remember(providers, searchQuery) {
        if (searchQuery.isEmpty()) {
            providers
        } else {
            providers.filter {
                it.name.contains(searchQuery, ignoreCase = true) ||
                it.providerType.contains(searchQuery, ignoreCase = true)
            }
        }
    }
    
    Surface(
        modifier = modifier.fillMaxHeight(),
        color = theme.sidebar,
        shadowElevation = 1.dp
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // 顶部标题和添加按钮
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Providers",
                    style = MaterialTheme.typography.titleLarge,
                    color = theme.sidebarForeground
                )
                
                IconButton(
                    onClick = onAddProvider
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Add Provider",
                        tint = theme.sidebarPrimary
                    )
                }
            }
            
            // 搜索栏
            OutlinedTextField(
                value = searchQuery,
                onValueChange = { searchQuery = it },
                placeholder = { 
                    Text(
                        "Search providers...",
                        color = theme.mutedForeground
                    )
                },
                leadingIcon = {
                    Icon(
                        Icons.Default.Search,
                        contentDescription = null,
                        tint = theme.mutedForeground
                    )
                },
                trailingIcon = {
                    if (searchQuery.isNotEmpty()) {
                        IconButton(
                            onClick = { searchQuery = "" }
                        ) {
                            Icon(
                                Icons.Default.Clear,
                                contentDescription = "Clear",
                                tint = theme.mutedForeground
                            )
                        }
                    }
                },
                singleLine = true,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = theme.sidebarPrimary,
                    unfocusedBorderColor = theme.sidebarBorder,
                    focusedTextColor = theme.sidebarForeground,
                    unfocusedTextColor = theme.sidebarForeground,
                    cursorColor = theme.sidebarPrimary
                )
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Provider列表
            LazyColumn(
                modifier = Modifier.weight(1f),
                contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                items(filteredProviders) { provider ->
                    ProviderItem(
                        provider = provider,
                        isSelected = provider == selectedProvider,
                        onClick = { onProviderSelected(provider) },
                        onToggleStatus = { onToggleProvider(provider) }
                    )
                }
            }
            
            // 底部统计
            HorizontalDivider(color = theme.sidebarBorder)
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "${providers.size} providers",
                    style = MaterialTheme.typography.bodySmall,
                    color = theme.mutedForeground
                )
                Text(
                    text = "${providers.count { it.isActive }} active",
                    style = MaterialTheme.typography.bodySmall,
                    color = theme.mutedForeground
                )
            }
        }
    }
}

@Composable
private fun ProviderItem(
    provider: LlmProviderModel,
    isSelected: Boolean,
    onClick: () -> Unit,
    onToggleStatus: () -> Unit
) {
    val theme = LocalShadcnTheme.current
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) theme.sidebarAccent else theme.sidebar
        ),
        shape = RoundedCornerShape(8.dp),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isSelected) 2.dp else 0.dp
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Provider图标
            Icon(
                imageVector = getProviderIcon(provider.providerType),
                contentDescription = null,
                modifier = Modifier.size(32.dp),
                tint = if (isSelected) theme.sidebarPrimaryForeground else theme.sidebarForeground
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // Provider信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = provider.name,
                    style = MaterialTheme.typography.titleMedium,
                    color = if (isSelected) theme.sidebarAccentForeground else theme.sidebarForeground,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Text(
                    text = provider.providerType.replaceFirstChar { it.uppercase() },
                    style = MaterialTheme.typography.bodySmall,
                    color = theme.mutedForeground
                )
            }
            
            // 状态开关
            Switch(
                checked = provider.isActive,
                onCheckedChange = { onToggleStatus() },
                colors = SwitchDefaults.colors(
                    checkedThumbColor = theme.primary,
                    checkedTrackColor = theme.primary.copy(alpha = 0.5f),
                    uncheckedThumbColor = theme.muted,
                    uncheckedTrackColor = theme.muted.copy(alpha = 0.5f)
                )
            )
        }
    }
}