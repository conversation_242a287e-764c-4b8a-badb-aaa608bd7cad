package com.chatbot.ui.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.chatbot.data.models.ModelCapability
import com.chatbot.theme.LocalShadcnTheme

/**
 * 模型能力标签组件
 * 显示模型支持的各种能力
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun ModelCapabilityChips(
    capabilities: Set<ModelCapability>,
    modifier: Modifier = Modifier
) {
    val theme = LocalShadcnTheme.current
    
    FlowRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(6.dp),
        verticalArrangement = Arrangement.spacedBy(6.dp)
    ) {
        capabilities.forEach { capability ->
            AssistChip(
                onClick = { },
                label = { 
                    Text(
                        text = capability.displayName,
                        style = MaterialTheme.typography.labelSmall
                    )
                },
                leadingIcon = {
                    Icon(
                        imageVector = getCapabilityIcon(capability),
                        contentDescription = null,
                        modifier = Modifier.size(14.dp)
                    )
                },
                colors = AssistChipDefaults.assistChipColors(
                    containerColor = theme.accent.copy(alpha = 0.1f),
                    labelColor = theme.accentForeground,
                    leadingIconContentColor = theme.accentForeground
                ),
                border = BorderStroke(
                    width = 1.dp,
                    color = theme.accent.copy(alpha = 0.3f)
                )
            )
        }
    }
}

/**
 * 模型信息显示组件
 * 显示模型的详细信息
 */
@Composable
fun ModelInfoDisplay(
    model: com.chatbot.data.models.LlmModel,
    modifier: Modifier = Modifier
) {
    val theme = LocalShadcnTheme.current
    
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = theme.card
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 基本信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = model.name,
                        style = MaterialTheme.typography.titleMedium,
                        color = theme.cardForeground
                    )
                    model.version?.let { version ->
                        Text(
                            text = "Version: $version",
                            style = MaterialTheme.typography.bodySmall,
                            color = theme.mutedForeground
                        )
                    }
                }
                
                // 状态标签
                AssistChip(
                    onClick = { },
                    label = { 
                        Text(
                            if (model.isActive) "Active" else "Inactive",
                            style = MaterialTheme.typography.labelSmall
                        )
                    },
                    colors = AssistChipDefaults.assistChipColors(
                        containerColor = if (model.isActive) 
                            theme.chart2.copy(alpha = 0.2f) 
                        else 
                            theme.muted,
                        labelColor = if (model.isActive) 
                            theme.chart2 
                        else 
                            theme.mutedForeground
                    )
                )
            }
            
            // 描述
            model.description?.let { desc ->
                Text(
                    text = desc,
                    style = MaterialTheme.typography.bodyMedium,
                    color = theme.cardForeground
                )
            }
            
            // 能力标签
            if (model.capabilities.isNotEmpty()) {
                ModelCapabilityChips(capabilities = model.capabilities)
            }
            
            HorizontalDivider(color = theme.border)
            
            // 技术规格
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // Token限制
                Column(
                    horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "${model.maxTokens}",
                        style = MaterialTheme.typography.titleLarge,
                        color = theme.primary
                    )
                    Text(
                        text = "Max Tokens",
                        style = MaterialTheme.typography.bodySmall,
                        color = theme.mutedForeground
                    )
                }
                
                // 输入价格
                if (model.pricingInput != null) {
                    Column(
                        horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "$${model.pricingInput}",
                            style = MaterialTheme.typography.titleLarge,
                            color = theme.chart1
                        )
                        Text(
                            text = "Input/1M",
                            style = MaterialTheme.typography.bodySmall,
                            color = theme.mutedForeground
                        )
                    }
                }
                
                // 输出价格
                if (model.pricingOutput != null) {
                    Column(
                        horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "$${model.pricingOutput}",
                            style = MaterialTheme.typography.titleLarge,
                            color = theme.chart3
                        )
                        Text(
                            text = "Output/1M",
                            style = MaterialTheme.typography.bodySmall,
                            color = theme.mutedForeground
                        )
                    }
                }
            }
        }
    }
}