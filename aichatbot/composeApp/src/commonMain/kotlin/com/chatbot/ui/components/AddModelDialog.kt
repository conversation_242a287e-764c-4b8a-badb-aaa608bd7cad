package com.chatbot.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.chatbot.data.models.LlmModel
import com.chatbot.data.models.ModelCapability
import com.chatbot.theme.LocalShadcnTheme
import kotlinx.datetime.Clock

/**
 * 添加/编辑模型对话框
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AddModelDialog(
    providerId: Int,
    editingModel: LlmModel? = null,
    onDismiss: () -> Unit,
    onConfirm: (LlmModel) -> Unit
) {
    val theme = LocalShadcnTheme.current
    val isEditing = editingModel != null
    
    // 表单状态
    var name by remember { mutableStateOf(editingModel?.name ?: "") }
    var version by remember { mutableStateOf(editingModel?.version ?: "") }
    var description by remember { mutableStateOf(editingModel?.description ?: "") }
    var maxTokens by remember { mutableStateOf(editingModel?.maxTokens?.toString() ?: "128000") }
    var pricingInput by remember { mutableStateOf(editingModel?.pricingInput?.toString() ?: "") }
    var pricingOutput by remember { mutableStateOf(editingModel?.pricingOutput?.toString() ?: "") }
    
    // 能力选择
    val selectedCapabilities = remember { 
        mutableStateSetOf<ModelCapability>().apply {
            editingModel?.capabilities?.let { addAll(it) }
        }
    }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = theme.popover,
        titleContentColor = theme.popoverForeground,
        textContentColor = theme.popoverForeground,
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    if (isEditing) Icons.Default.Edit else Icons.Default.Add,
                    contentDescription = null,
                    tint = theme.primary
                )
                Text(if (isEditing) "Edit Model" else "Add Model")
            }
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .verticalScroll(rememberScrollState()),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Model Name
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("Model Name *") },
                    placeholder = { Text("e.g., gpt-4-turbo") },
                    leadingIcon = {
                        Icon(Icons.Default.Label, contentDescription = null)
                    },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = theme.primary,
                        unfocusedBorderColor = theme.border,
                        focusedLabelColor = theme.primary,
                        unfocusedLabelColor = theme.mutedForeground,
                        focusedTextColor = theme.popoverForeground,
                        unfocusedTextColor = theme.popoverForeground
                    )
                )
                
                // Version
                OutlinedTextField(
                    value = version,
                    onValueChange = { version = it },
                    label = { Text("Version") },
                    placeholder = { Text("e.g., 2024-01-01") },
                    leadingIcon = {
                        Icon(Icons.Default.Code, contentDescription = null)
                    },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = theme.primary,
                        unfocusedBorderColor = theme.border,
                        focusedLabelColor = theme.primary,
                        unfocusedLabelColor = theme.mutedForeground,
                        focusedTextColor = theme.popoverForeground,
                        unfocusedTextColor = theme.popoverForeground
                    )
                )
                
                // Description
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("Description") },
                    leadingIcon = {
                        Icon(Icons.Default.Description, contentDescription = null)
                    },
                    minLines = 2,
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = theme.primary,
                        unfocusedBorderColor = theme.border,
                        focusedLabelColor = theme.primary,
                        unfocusedLabelColor = theme.mutedForeground,
                        focusedTextColor = theme.popoverForeground,
                        unfocusedTextColor = theme.popoverForeground
                    )
                )
                
                // Max Tokens
                OutlinedTextField(
                    value = maxTokens,
                    onValueChange = { maxTokens = it },
                    label = { Text("Max Tokens") },
                    leadingIcon = {
                        Icon(Icons.Default.DataUsage, contentDescription = null)
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = theme.primary,
                        unfocusedBorderColor = theme.border,
                        focusedLabelColor = theme.primary,
                        unfocusedLabelColor = theme.mutedForeground,
                        focusedTextColor = theme.popoverForeground,
                        unfocusedTextColor = theme.popoverForeground
                    )
                )
                
                // Pricing
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedTextField(
                        value = pricingInput,
                        onValueChange = { pricingInput = it },
                        label = { Text("Input Price $/1M") },
                        leadingIcon = {
                            Icon(Icons.Default.AttachMoney, contentDescription = null)
                        },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        singleLine = true,
                        modifier = Modifier.weight(1f),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = theme.primary,
                            unfocusedBorderColor = theme.border,
                            focusedLabelColor = theme.primary,
                            unfocusedLabelColor = theme.mutedForeground,
                            focusedTextColor = theme.popoverForeground,
                            unfocusedTextColor = theme.popoverForeground
                        )
                    )
                    
                    OutlinedTextField(
                        value = pricingOutput,
                        onValueChange = { pricingOutput = it },
                        label = { Text("Output Price $/1M") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        singleLine = true,
                        modifier = Modifier.weight(1f),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = theme.primary,
                            unfocusedBorderColor = theme.border,
                            focusedLabelColor = theme.primary,
                            unfocusedLabelColor = theme.mutedForeground,
                            focusedTextColor = theme.popoverForeground,
                            unfocusedTextColor = theme.popoverForeground
                        )
                    )
                }
                
                // Capabilities
                Column {
                    Text(
                        text = "Capabilities",
                        style = MaterialTheme.typography.bodyMedium,
                        color = theme.popoverForeground
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    FlowRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        ModelCapability.entries.forEach { capability ->
                            FilterChip(
                                selected = capability in selectedCapabilities,
                                onClick = {
                                    if (capability in selectedCapabilities) {
                                        selectedCapabilities.remove(capability)
                                    } else {
                                        selectedCapabilities.add(capability)
                                    }
                                },
                                label = { 
                                    Text(
                                        capability.displayName,
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                },
                                leadingIcon = {
                                    Icon(
                                        getCapabilityIcon(capability),
                                        contentDescription = null,
                                        modifier = Modifier.size(16.dp)
                                    )
                                },
                                colors = FilterChipDefaults.filterChipColors(
                                    selectedContainerColor = theme.primary.copy(alpha = 0.2f),
                                    selectedLabelColor = theme.primary,
                                    selectedLeadingIconColor = theme.primary
                                )
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    val now = Clock.System.now().toEpochMilliseconds()
                    val model = if (isEditing) {
                        editingModel!!.copy(
                            name = name,
                            version = version.ifEmpty { null },
                            description = description.ifEmpty { null },
                            maxTokens = maxTokens.toIntOrNull() ?: 128000,
                            pricingInput = pricingInput.toDoubleOrNull(),
                            pricingOutput = pricingOutput.toDoubleOrNull(),
                            capabilities = selectedCapabilities.toSet(),
                            updatedAt = now
                        )
                    } else {
                        LlmModel(
                            providerId = providerId,
                            name = name,
                            version = version.ifEmpty { null },
                            description = description.ifEmpty { null },
                            maxTokens = maxTokens.toIntOrNull() ?: 128000,
                            pricingInput = pricingInput.toDoubleOrNull(),
                            pricingOutput = pricingOutput.toDoubleOrNull(),
                            capabilities = selectedCapabilities.toSet(),
                            createdAt = now,
                            updatedAt = now
                        )
                    }
                    onConfirm(model)
                },
                enabled = name.isNotEmpty(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = theme.primary,
                    contentColor = theme.primaryForeground
                )
            ) {
                Text(if (isEditing) "Save" else "Add")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = theme.mutedForeground
                )
            ) {
                Text("Cancel")
            }
        }
    )
}