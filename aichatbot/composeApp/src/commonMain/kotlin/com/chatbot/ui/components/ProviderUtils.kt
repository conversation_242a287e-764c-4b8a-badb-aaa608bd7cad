package com.chatbot.ui.components

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.vector.ImageVector
import com.chatbot.data.models.LlmProviderModel
import com.chatbot.data.models.ModelCapability

/**
 * Provider相关的工具函数
 */

/**
 * 获取Provider图标
 */
fun getProviderIcon(providerType: String): ImageVector {
    return when (providerType) {
        LlmProviderModel.OPENAI -> Icons.Default.SmartToy
        LlmProviderModel.GEMINI -> Icons.Default.AutoAwesome
        LlmProviderModel.ANTHROPIC -> Icons.Default.Psychology
        LlmProviderModel.OPENAI_COMPATIBLE -> Icons.Default.Extension
        else -> Icons.Default.CloudQueue
    }
}

/**
 * 获取模型能力图标
 */
fun getCapabilityIcon(capability: ModelCapability): ImageVector {
    return when (capability) {
        ModelCapability.CHAT -> Icons.Default.Chat
        ModelCapability.COMPLETION -> Icons.Default.TextFields
        ModelCapability.EMBEDDING -> Icons.Default.DataArray
        ModelCapability.VISION -> Icons.Default.RemoveRedEye
        ModelCapability.FUNCTION_CALLING -> Icons.Default.Functions
        ModelCapability.STREAMING -> Icons.Default.Stream
        ModelCapability.JSON_MODE -> Icons.Default.Code
        ModelCapability.CODE_EXECUTION -> Icons.Default.Terminal
        ModelCapability.WEB_SEARCH -> Icons.Default.Search
        ModelCapability.FILE_UPLOAD -> Icons.Default.UploadFile
        ModelCapability.VOICE -> Icons.Default.Mic
        ModelCapability.VIDEO -> Icons.Default.VideoLibrary
    }
}

/**
 * 获取Provider类型的显示名称
 */
fun getProviderTypeDisplayName(providerType: String): String {
    return when (providerType) {
        LlmProviderModel.OPENAI -> "OpenAI"
        LlmProviderModel.GEMINI -> "Google Gemini"
        LlmProviderModel.ANTHROPIC -> "Anthropic"
        LlmProviderModel.OPENAI_COMPATIBLE -> "OpenAI Compatible"
        else -> providerType
    }
}

/**
 * 获取默认的API Base URL
 */
fun getDefaultBaseUrl(providerType: String): String? {
    return when (providerType) {
        LlmProviderModel.OPENAI -> "https://api.openai.com"
        LlmProviderModel.GEMINI -> "https://generativelanguage.googleapis.com"
        LlmProviderModel.ANTHROPIC -> "https://api.anthropic.com"
        else -> null
    }
}