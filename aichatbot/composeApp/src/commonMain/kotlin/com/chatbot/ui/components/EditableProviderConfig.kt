package com.chatbot.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import com.chatbot.data.models.LlmProviderModel
import com.chatbot.theme.LocalShadcnTheme
import kotlinx.datetime.Clock

/**
 * 可编辑的Provider配置组件
 */
@Composable
fun EditableProviderConfig(
    provider: LlmProviderModel,
    onSave: (LlmProviderModel) -> Unit,
    onCancel: () -> Unit
) {
    val theme = LocalShadcnTheme.current
    
    var name by remember(provider) { mutableStateOf(provider.name) }
    var description by remember(provider) { mutableStateOf(provider.description ?: "") }
    var apiKey by remember(provider) { mutableStateOf(provider.apiKey ?: "") }
    var baseUrl by remember(provider) { mutableStateOf(provider.baseUrl ?: "") }
    var proxyUrl by remember(provider) { mutableStateOf(provider.proxyUrl ?: "") }
    var apiVersion by remember(provider) { mutableStateOf(provider.apiVersion ?: "") }
    var timeoutSeconds by remember(provider) { mutableStateOf(provider.timeoutSeconds.toString()) }
    var maxRetries by remember(provider) { mutableStateOf(provider.maxRetries.toString()) }
    
    var showApiKey by remember { mutableStateOf(false) }
    var hasChanges by remember { mutableStateOf(false) }
    
    // 检测是否有改变
    LaunchedEffect(name, description, apiKey, baseUrl, proxyUrl, apiVersion, timeoutSeconds, maxRetries) {
        hasChanges = name != provider.name ||
                description != (provider.description ?: "") ||
                apiKey != (provider.apiKey ?: "") ||
                baseUrl != (provider.baseUrl ?: "") ||
                proxyUrl != (provider.proxyUrl ?: "") ||
                apiVersion != (provider.apiVersion ?: "") ||
                timeoutSeconds != provider.timeoutSeconds.toString() ||
                maxRetries != provider.maxRetries.toString()
    }
    
    Card(
        colors = CardDefaults.cardColors(
            containerColor = theme.card
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Edit Configuration",
                    style = MaterialTheme.typography.titleLarge,
                    color = theme.cardForeground
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    TextButton(
                        onClick = onCancel,
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = theme.mutedForeground
                        )
                    ) {
                        Text("Cancel")
                    }
                    
                    Button(
                        onClick = {
                            val updatedProvider = provider.copy(
                                name = name,
                                description = description.ifEmpty { null },
                                apiKey = apiKey.ifEmpty { null },
                                baseUrl = baseUrl.ifEmpty { null },
                                proxyUrl = proxyUrl.ifEmpty { null },
                                apiVersion = apiVersion.ifEmpty { null },
                                timeoutSeconds = timeoutSeconds.toIntOrNull() ?: 60,
                                maxRetries = maxRetries.toIntOrNull() ?: 3,
                                updatedAt = Clock.System.now().toEpochMilliseconds()
                            )
                            onSave(updatedProvider)
                        },
                        enabled = hasChanges && name.isNotEmpty(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = theme.primary,
                            contentColor = theme.primaryForeground
                        )
                    ) {
                        Text("Save")
                    }
                }
            }
            
            HorizontalDivider(color = theme.border)
            
            // Name
            OutlinedTextField(
                value = name,
                onValueChange = { name = it },
                label = { Text("Name *") },
                leadingIcon = {
                    Icon(Icons.Default.Label, contentDescription = null)
                },
                singleLine = true,
                modifier = Modifier.fillMaxWidth(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = theme.primary,
                    unfocusedBorderColor = theme.border,
                    focusedLabelColor = theme.primary,
                    unfocusedLabelColor = theme.mutedForeground
                )
            )
            
            // Description
            OutlinedTextField(
                value = description,
                onValueChange = { description = it },
                label = { Text("Description") },
                leadingIcon = {
                    Icon(Icons.Default.Description, contentDescription = null)
                },
                minLines = 2,
                modifier = Modifier.fillMaxWidth(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = theme.primary,
                    unfocusedBorderColor = theme.border,
                    focusedLabelColor = theme.primary,
                    unfocusedLabelColor = theme.mutedForeground
                )
            )
            
            // API Key
            OutlinedTextField(
                value = apiKey,
                onValueChange = { apiKey = it },
                label = { Text("API Key") },
                leadingIcon = {
                    Icon(Icons.Default.Key, contentDescription = null)
                },
                trailingIcon = {
                    IconButton(
                        onClick = { showApiKey = !showApiKey }
                    ) {
                        Icon(
                            if (showApiKey) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                            contentDescription = if (showApiKey) "Hide" else "Show"
                        )
                    }
                },
                visualTransformation = if (showApiKey) VisualTransformation.None else PasswordVisualTransformation(),
                singleLine = true,
                modifier = Modifier.fillMaxWidth(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = theme.primary,
                    unfocusedBorderColor = theme.border,
                    focusedLabelColor = theme.primary,
                    unfocusedLabelColor = theme.mutedForeground
                )
            )
            
            // Base URL
            OutlinedTextField(
                value = baseUrl,
                onValueChange = { baseUrl = it },
                label = { Text("Base URL") },
                placeholder = { Text(getDefaultBaseUrl(provider.providerType) ?: "") },
                leadingIcon = {
                    Icon(Icons.Default.Link, contentDescription = null)
                },
                singleLine = true,
                modifier = Modifier.fillMaxWidth(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = theme.primary,
                    unfocusedBorderColor = theme.border,
                    focusedLabelColor = theme.primary,
                    unfocusedLabelColor = theme.mutedForeground
                )
            )
            
            // Proxy URL
            OutlinedTextField(
                value = proxyUrl,
                onValueChange = { proxyUrl = it },
                label = { Text("Proxy URL") },
                leadingIcon = {
                    Icon(Icons.Default.VpnKey, contentDescription = null)
                },
                singleLine = true,
                modifier = Modifier.fillMaxWidth(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = theme.primary,
                    unfocusedBorderColor = theme.border,
                    focusedLabelColor = theme.primary,
                    unfocusedLabelColor = theme.mutedForeground
                )
            )
            
            // API Version (for Anthropic)
            if (provider.providerType == LlmProviderModel.ANTHROPIC) {
                OutlinedTextField(
                    value = apiVersion,
                    onValueChange = { apiVersion = it },
                    label = { Text("API Version") },
                    placeholder = { Text("2023-06-01") },
                    leadingIcon = {
                        Icon(Icons.Default.Code, contentDescription = null)
                    },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = theme.primary,
                        unfocusedBorderColor = theme.border,
                        focusedLabelColor = theme.primary,
                        unfocusedLabelColor = theme.mutedForeground
                    )
                )
            }
            
            // Timeout and Retries
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                OutlinedTextField(
                    value = timeoutSeconds,
                    onValueChange = { timeoutSeconds = it },
                    label = { Text("Timeout (seconds)") },
                    leadingIcon = {
                        Icon(Icons.Default.Timer, contentDescription = null)
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true,
                    modifier = Modifier.weight(1f),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = theme.primary,
                        unfocusedBorderColor = theme.border,
                        focusedLabelColor = theme.primary,
                        unfocusedLabelColor = theme.mutedForeground
                    )
                )
                
                OutlinedTextField(
                    value = maxRetries,
                    onValueChange = { maxRetries = it },
                    label = { Text("Max Retries") },
                    leadingIcon = {
                        Icon(Icons.Default.Replay, contentDescription = null)
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true,
                    modifier = Modifier.weight(1f),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = theme.primary,
                        unfocusedBorderColor = theme.border,
                        focusedLabelColor = theme.primary,
                        unfocusedLabelColor = theme.mutedForeground
                    )
                )
            }
        }
    }
}