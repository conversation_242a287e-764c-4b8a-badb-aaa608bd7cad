package com.chatbot.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import com.chatbot.data.models.LlmProviderModel
import com.chatbot.theme.LocalShadcnTheme
import kotlinx.datetime.Clock

/**
 * Provider详情视图组件
 * 
 * 显示和编辑Provider的配置信息
 */
@Composable
fun ProviderDetailView(
    provider: LlmProviderModel,
    onUpdateProvider: (LlmProviderModel) -> Unit,
    onDeleteProvider: () -> Unit,
    onManageModels: () -> Unit,
    onTestConnection: (LlmProviderModel) -> Unit,
    modifier: Modifier = Modifier
) {
    val theme = LocalShadcnTheme.current
    var isEditing by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    
    // 重置编辑状态当provider改变
    LaunchedEffect(provider.id) {
        isEditing = false
    }
    
    Surface(
        modifier = modifier,
        color = theme.background
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(24.dp)
        ) {
            // 标题栏
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = provider.name,
                        style = MaterialTheme.typography.headlineMedium,
                        color = theme.foreground
                    )
                    Text(
                        text = getProviderTypeDisplayName(provider.providerType),
                        style = MaterialTheme.typography.bodyMedium,
                        color = theme.mutedForeground
                    )
                }
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 测试连接按钮
                    OutlinedButton(
                        onClick = { onTestConnection(provider) },
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = theme.primary
                        )
                    ) {
                        Icon(
                            Icons.Default.WifiTethering,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Test Connection")
                    }
                    
                    // 管理模型按钮
                    Button(
                        onClick = onManageModels,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = theme.primary,
                            contentColor = theme.primaryForeground
                        )
                    ) {
                        Icon(
                            Icons.Default.ModelTraining,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Manage Models")
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 状态卡片
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = theme.card
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        // 状态
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                if (provider.isActive) Icons.Default.CheckCircle else Icons.Default.Cancel,
                                contentDescription = null,
                                tint = if (provider.isActive) theme.chart2 else theme.destructive,
                                modifier = Modifier.size(20.dp)
                            )
                            Text(
                                text = if (provider.isActive) "Active" else "Inactive",
                                style = MaterialTheme.typography.bodyMedium,
                                color = theme.cardForeground
                            )
                        }
                        
                        // 创建时间
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                Icons.Default.Schedule,
                                contentDescription = null,
                                tint = theme.mutedForeground,
                                modifier = Modifier.size(20.dp)
                            )
                            Text(
                                text = "Created: ${formatTimestamp(provider.createdAt)}",
                                style = MaterialTheme.typography.bodyMedium,
                                color = theme.mutedForeground
                            )
                        }
                    }
                    
                    // 操作按钮
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        IconButton(
                            onClick = { isEditing = !isEditing }
                        ) {
                            Icon(
                                if (isEditing) Icons.Default.Close else Icons.Default.Edit,
                                contentDescription = if (isEditing) "Cancel Edit" else "Edit",
                                tint = theme.primary
                            )
                        }
                        
                        IconButton(
                            onClick = { showDeleteDialog = true }
                        ) {
                            Icon(
                                Icons.Default.Delete,
                                contentDescription = "Delete",
                                tint = theme.destructive
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 配置信息
            if (isEditing) {
                EditableProviderConfig(
                    provider = provider,
                    onSave = { updatedProvider ->
                        onUpdateProvider(updatedProvider)
                        isEditing = false
                    },
                    onCancel = {
                        isEditing = false
                    }
                )
            } else {
                ProviderConfigDisplay(provider = provider)
            }
        }
    }
    
    // 删除确认对话框
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            containerColor = theme.popover,
            titleContentColor = theme.popoverForeground,
            textContentColor = theme.popoverForeground,
            title = {
                Text("Delete Provider")
            },
            text = {
                Text("Are you sure you want to delete \"${provider.name}\"? This will also delete all associated models.")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        onDeleteProvider()
                        showDeleteDialog = false
                    },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = theme.destructive
                    )
                ) {
                    Text("Delete")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteDialog = false },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = theme.mutedForeground
                    )
                ) {
                    Text("Cancel")
                }
            }
        )
    }
}

@Composable
private fun ProviderConfigDisplay(
    provider: LlmProviderModel
) {
    val theme = LocalShadcnTheme.current
    
    Card(
        colors = CardDefaults.cardColors(
            containerColor = theme.card
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "Configuration",
                style = MaterialTheme.typography.titleLarge,
                color = theme.cardForeground
            )
            
            HorizontalDivider(color = theme.border)
            
            // API Key
            ConfigItem(
                label = "API Key",
                value = provider.apiKey?.let { "••••••••${it.takeLast(4)}" } ?: "Not configured",
                icon = Icons.Default.Key
            )
            
            // Base URL
            ConfigItem(
                label = "Base URL",
                value = provider.baseUrl ?: getDefaultBaseUrl(provider.providerType) ?: "Default",
                icon = Icons.Default.Link
            )
            
            // Proxy URL
            if (!provider.proxyUrl.isNullOrEmpty()) {
                ConfigItem(
                    label = "Proxy URL",
                    value = provider.proxyUrl,
                    icon = Icons.Default.VpnKey
                )
            }
            
            // API Version
            if (!provider.apiVersion.isNullOrEmpty()) {
                ConfigItem(
                    label = "API Version",
                    value = provider.apiVersion,
                    icon = Icons.Default.Code
                )
            }
            
            // Timeout
            ConfigItem(
                label = "Timeout",
                value = "${provider.timeoutSeconds} seconds",
                icon = Icons.Default.Timer
            )
            
            // Max Retries
            ConfigItem(
                label = "Max Retries",
                value = provider.maxRetries.toString(),
                icon = Icons.Default.Replay
            )
            
            // Description
            if (!provider.description.isNullOrEmpty()) {
                ConfigItem(
                    label = "Description",
                    value = provider.description,
                    icon = Icons.Default.Description
                )
            }
        }
    }
}

@Composable
private fun ConfigItem(
    label: String,
    value: String,
    icon: ImageVector
) {
    val theme = LocalShadcnTheme.current
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalAlignment = Alignment.Top
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = theme.mutedForeground,
            modifier = Modifier.size(20.dp)
        )
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodySmall,
                color = theme.mutedForeground
            )
            Text(
                text = value,
                style = MaterialTheme.typography.bodyMedium,
                color = theme.cardForeground
            )
        }
    }
}

private fun formatTimestamp(timestamp: Long): String {
    // Simple date formatting - you might want to use a proper date formatter
    val date = kotlinx.datetime.Instant.fromEpochMilliseconds(timestamp)
    val now = Clock.System.now()
    val diff = now - date
    
    return when {
        diff.inWholeDays > 30 -> "${diff.inWholeDays / 30} months ago"
        diff.inWholeDays > 0 -> "${diff.inWholeDays} days ago"
        diff.inWholeHours > 0 -> "${diff.inWholeHours} hours ago"
        diff.inWholeMinutes > 0 -> "${diff.inWholeMinutes} minutes ago"
        else -> "Just now"
    }
}