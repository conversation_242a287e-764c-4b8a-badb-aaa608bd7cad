package com.chatbot.ui.pages

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.chatbot.data.models.LlmProviderModel
import com.chatbot.theme.LocalShadcnTheme
import com.chatbot.ui.LlmProvider
import com.chatbot.ui.components.*
import kotlinx.coroutines.launch
import org.koin.compose.koinInject

/**
 * LLM提供商设置页面
 *
 * 简洁的左右分栏设计：
 * - 左侧：提供商列表 + 搜索
 * - 右侧：配置面板 + 模型管理
 */
@Composable
fun ProviderSettingsPage(
    modifier: Modifier = Modifier
) {
    val viewModel: LlmProvider = koinInject()
    val providers by viewModel.providers.collectAsState()
    val selectedProvider by viewModel.selectedProvider.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    
    val scope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }
    
    var showAddProviderDialog by remember { mutableStateOf(false) }
    var showModelManagerDialog by remember { mutableStateOf(false) }
    
    val theme = LocalShadcnTheme.current
    
    // 初始化加载数据
    LaunchedEffect(Unit) {
        viewModel.loadProviders()
    }
    
    // 显示错误
    LaunchedEffect(error) {
        error?.let {
            scope.launch {
                snackbarHostState.showSnackbar(
                    message = it,
                    duration = SnackbarDuration.Short
                )
                viewModel.clearError()
            }
        }
    }
    
    Scaffold(
        modifier = modifier,
        snackbarHost = {
            SnackbarHost(
                hostState = snackbarHostState,
                snackbar = { data ->
                    Snackbar(
                        snackbarData = data,
                        containerColor = theme.popover,
                        contentColor = theme.popoverForeground,
                        actionColor = theme.primary
                    )
                }
            )
        },
        containerColor = theme.background
    ) { paddingValues ->
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 左侧边栏
            ProviderSidebar(
                providers = providers,
                selectedProvider = selectedProvider,
                onProviderSelected = { provider ->
                    viewModel.selectProvider(provider)
                },
                onToggleProvider = { provider ->
                    viewModel.toggleProviderStatus(
                        provider = provider,
                        onError = { errorMsg ->
                            scope.launch {
                                snackbarHostState.showSnackbar(errorMsg)
                            }
                        }
                    )
                },
                onAddProvider = {
                    showAddProviderDialog = true
                },
                modifier = Modifier.width(320.dp)
            )
            
            // 分隔线
            VerticalDivider(
                modifier = Modifier.fillMaxHeight(),
                color = theme.border
            )
            
            // 右侧内容区
            Box(
                modifier = Modifier.fillMaxSize()
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center),
                        color = theme.primary
                    )
                } else if (selectedProvider != null) {
                    ProviderDetailView(
                        provider = selectedProvider!!,
                        onUpdateProvider = { updatedProvider ->
                            viewModel.updateProvider(
                                provider = updatedProvider,
                                onError = { errorMsg ->
                                    scope.launch {
                                        snackbarHostState.showSnackbar(errorMsg)
                                    }
                                }
                            )
                        },
                        onDeleteProvider = {
                            selectedProvider?.id?.let { id ->
                                viewModel.deleteProvider(
                                    providerId = id,
                                    onError = { errorMsg ->
                                        scope.launch {
                                            snackbarHostState.showSnackbar(errorMsg)
                                        }
                                    }
                                )
                            }
                        },
                        onManageModels = {
                            showModelManagerDialog = true
                        },
                        onTestConnection = { provider ->
                            viewModel.testConnection(provider) { result ->
                                scope.launch {
                                    snackbarHostState.showSnackbar(
                                        message = result.message,
                                        duration = SnackbarDuration.Short
                                    )
                                }
                            }
                        },
                        modifier = Modifier.fillMaxSize()
                    )
                } else {
                    // 空状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = "No provider selected",
                                style = MaterialTheme.typography.headlineMedium,
                                color = theme.mutedForeground
                            )
                            if (providers.isEmpty()) {
                                Button(
                                    onClick = { showAddProviderDialog = true },
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = theme.primary,
                                        contentColor = theme.primaryForeground
                                    )
                                ) {
                                    Text("Add Your First Provider")
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 对话框
    if (showAddProviderDialog) {
        AddProviderDialog(
            onDismiss = { showAddProviderDialog = false },
            onConfirm = { provider ->
                viewModel.createProvider(
                    provider = provider,
                    onSuccess = {
                        showAddProviderDialog = false
                        scope.launch {
                            snackbarHostState.showSnackbar("Provider added successfully")
                        }
                    },
                    onError = { errorMsg ->
                        scope.launch {
                            snackbarHostState.showSnackbar(errorMsg)
                        }
                    }
                )
            }
        )
    }
    
    if (showModelManagerDialog && selectedProvider != null) {
        ModelManagerDialog(
            provider = selectedProvider!!,
            onDismiss = { showModelManagerDialog = false }
        )
    }
}