package com.chatbot.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.chatbot.data.models.LlmModel
import com.chatbot.theme.LocalShadcnTheme

/**
 * 模型列表项组件
 */
@Composable
fun ModelListItem(
    model: LlmModel,
    onEdit: () -> Unit,
    onDelete: () -> Unit,
    onToggleStatus: () -> Unit
) {
    val theme = LocalShadcnTheme.current
    var showDeleteDialog by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = theme.card
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 顶部行：名称、版本、状态
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    modifier = Modifier.weight(1f),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.SmartToy,
                        contentDescription = null,
                        tint = theme.primary,
                        modifier = Modifier.size(24.dp)
                    )
                    
                    Column {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = model.name,
                                style = MaterialTheme.typography.titleMedium,
                                color = theme.cardForeground,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                            
                            model.version?.let { version ->
                                Text(
                                    text = version,
                                    style = MaterialTheme.typography.bodySmall,
                                    color = theme.mutedForeground
                                )
                            }
                        }
                        
                        model.description?.let { desc ->
                            Text(
                                text = desc,
                                style = MaterialTheme.typography.bodySmall,
                                color = theme.mutedForeground,
                                maxLines = 2,
                                overflow = TextOverflow.Ellipsis
                            )
                        }
                    }
                }
                
                // 操作按钮
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Switch(
                        checked = model.isActive,
                        onCheckedChange = { onToggleStatus() },
                        colors = SwitchDefaults.colors(
                            checkedThumbColor = theme.primary,
                            checkedTrackColor = theme.primary.copy(alpha = 0.5f),
                            uncheckedThumbColor = theme.muted,
                            uncheckedTrackColor = theme.muted.copy(alpha = 0.5f)
                        )
                    )
                    
                    IconButton(onClick = onEdit) {
                        Icon(
                            Icons.Default.Edit,
                            contentDescription = "Edit",
                            tint = theme.primary
                        )
                    }
                    
                    IconButton(onClick = { showDeleteDialog = true }) {
                        Icon(
                            Icons.Default.Delete,
                            contentDescription = "Delete",
                            tint = theme.destructive
                        )
                    }
                }
            }
            
            // 能力标签
            if (model.capabilities.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                ModelCapabilityChips(capabilities = model.capabilities)
            }
            
            // 额外信息：定价、Token限制
            if (model.pricingInput != null || model.pricingOutput != null || model.maxTokens > 0) {
                Spacer(modifier = Modifier.height(12.dp))
                HorizontalDivider(color = theme.border)
                Spacer(modifier = Modifier.height(12.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(24.dp)
                ) {
                    // 定价信息
                    if (model.pricingInput != null || model.pricingOutput != null) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                Icons.Default.AttachMoney,
                                contentDescription = null,
                                tint = theme.mutedForeground,
                                modifier = Modifier.size(16.dp)
                            )
                            Text(
                                text = buildString {
                                    if (model.pricingInput != null) {
                                        append("Input: $${model.pricingInput}/1M")
                                    }
                                    if (model.pricingInput != null && model.pricingOutput != null) {
                                        append(" | ")
                                    }
                                    if (model.pricingOutput != null) {
                                        append("Output: $${model.pricingOutput}/1M")
                                    }
                                },
                                style = MaterialTheme.typography.bodySmall,
                                color = theme.mutedForeground
                            )
                        }
                    }
                    
                    // Token限制
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            Icons.Default.DataUsage,
                            contentDescription = null,
                            tint = theme.mutedForeground,
                            modifier = Modifier.size(16.dp)
                        )
                        Text(
                            text = "${model.maxTokens} tokens",
                            style = MaterialTheme.typography.bodySmall,
                            color = theme.mutedForeground
                        )
                    }
                }
            }
        }
    }
    
    // 删除确认对话框
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            containerColor = theme.popover,
            titleContentColor = theme.popoverForeground,
            textContentColor = theme.popoverForeground,
            title = {
                Text("Delete Model")
            },
            text = {
                Text("Are you sure you want to delete \"${model.name}\"?")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        onDelete()
                        showDeleteDialog = false
                    },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = theme.destructive
                    )
                ) {
                    Text("Delete")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteDialog = false },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = theme.mutedForeground
                    )
                ) {
                    Text("Cancel")
                }
            }
        )
    }
}