package com.chatbot.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.chatbot.data.models.LlmModel
import com.chatbot.data.models.LlmProviderModel
import com.chatbot.theme.LocalShadcnTheme
import com.chatbot.ui.LlmProvider
import kotlinx.coroutines.launch
import org.koin.compose.koinInject

/**
 * 模型管理对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ModelManagerDialog(
    provider: LlmProviderModel,
    onDismiss: () -> Unit
) {
    val theme = LocalShadcnTheme.current
    val viewModel: LlmProvider = koinInject()
    val models by viewModel.models.collectAsState()
    
    var showAddModelDialog by remember { mutableStateOf(false) }
    var editingModel by remember { mutableStateOf<LlmModel?>(null) }
    var searchQuery by remember { mutableStateOf("") }
    
    val scope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }
    
    // 过滤模型
    val filteredModels = remember(models, searchQuery) {
        val providerModels = models.filter { it.providerId == provider.id }
        if (searchQuery.isEmpty()) {
            providerModels
        } else {
            providerModels.filter {
                it.name.contains(searchQuery, ignoreCase = true) ||
                it.description?.contains(searchQuery, ignoreCase = true) == true
            }
        }
    }
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .fillMaxHeight(0.8f),
            shape = RoundedCornerShape(16.dp),
            color = theme.popover,
            shadowElevation = 8.dp
        ) {
            Scaffold(
                topBar = {
                    TopAppBar(
                        title = {
                            Column {
                                Text(
                                    "Manage Models",
                                    style = MaterialTheme.typography.titleLarge
                                )
                                Text(
                                    provider.name,
                                    style = MaterialTheme.typography.bodySmall,
                                    color = theme.mutedForeground
                                )
                            }
                        },
                        navigationIcon = {
                            IconButton(onClick = onDismiss) {
                                Icon(
                                    Icons.Default.Close,
                                    contentDescription = "Close"
                                )
                            }
                        },
                        actions = {
                            IconButton(
                                onClick = { showAddModelDialog = true }
                            ) {
                                Icon(
                                    Icons.Default.Add,
                                    contentDescription = "Add Model",
                                    tint = theme.primary
                                )
                            }
                        },
                        colors = TopAppBarDefaults.topAppBarColors(
                            containerColor = theme.popover,
                            titleContentColor = theme.popoverForeground
                        )
                    )
                },
                snackbarHost = {
                    SnackbarHost(
                        hostState = snackbarHostState,
                        snackbar = { data ->
                            Snackbar(
                                snackbarData = data,
                                containerColor = theme.popover,
                                contentColor = theme.popoverForeground,
                                actionColor = theme.primary
                            )
                        }
                    )
                },
                containerColor = theme.popover
            ) { paddingValues ->
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues)
                ) {
                    // 搜索栏
                    OutlinedTextField(
                        value = searchQuery,
                        onValueChange = { searchQuery = it },
                        placeholder = { Text("Search models...") },
                        leadingIcon = {
                            Icon(
                                Icons.Default.Search,
                                contentDescription = null,
                                tint = theme.mutedForeground
                            )
                        },
                        trailingIcon = {
                            if (searchQuery.isNotEmpty()) {
                                IconButton(
                                    onClick = { searchQuery = "" }
                                ) {
                                    Icon(
                                        Icons.Default.Clear,
                                        contentDescription = "Clear",
                                        tint = theme.mutedForeground
                                    )
                                }
                            }
                        },
                        singleLine = true,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = theme.primary,
                            unfocusedBorderColor = theme.border,
                            focusedTextColor = theme.popoverForeground,
                            unfocusedTextColor = theme.popoverForeground
                        )
                    )
                    
                    // 模型列表
                    if (filteredModels.isEmpty()) {
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(32.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.spacedBy(16.dp)
                            ) {
                                Icon(
                                    Icons.Default.ModelTraining,
                                    contentDescription = null,
                                    modifier = Modifier.size(64.dp),
                                    tint = theme.mutedForeground
                                )
                                Text(
                                    text = if (searchQuery.isNotEmpty()) 
                                        "No models found" 
                                    else 
                                        "No models configured",
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = theme.mutedForeground
                                )
                                if (searchQuery.isEmpty()) {
                                    Button(
                                        onClick = { showAddModelDialog = true },
                                        colors = ButtonDefaults.buttonColors(
                                            containerColor = theme.primary,
                                            contentColor = theme.primaryForeground
                                        )
                                    ) {
                                        Icon(
                                            Icons.Default.Add,
                                            contentDescription = null,
                                            modifier = Modifier.size(18.dp)
                                        )
                                        Spacer(modifier = Modifier.width(4.dp))
                                        Text("Add First Model")
                                    }
                                }
                            }
                        }
                    } else {
                        LazyColumn(
                            contentPadding = PaddingValues(16.dp),
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            items(filteredModels) { model ->
                                ModelListItem(
                                    model = model,
                                    onEdit = { editingModel = model },
                                    onDelete = {
                                        model.id?.let { modelId ->
                                            viewModel.deleteModel(
                                                modelId = modelId,
                                                onSuccess = {
                                                    scope.launch {
                                                        snackbarHostState.showSnackbar("Model deleted")
                                                    }
                                                },
                                                onError = { error ->
                                                    scope.launch {
                                                        snackbarHostState.showSnackbar(error)
                                                    }
                                                }
                                            )
                                        }
                                    },
                                    onToggleStatus = {
                                        viewModel.toggleModelStatus(
                                            model = model,
                                            onError = { error ->
                                                scope.launch {
                                                    snackbarHostState.showSnackbar(error)
                                                }
                                            }
                                        )
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 添加模型对话框
    if (showAddModelDialog) {
        provider.id?.let { providerId ->
            AddModelDialog(
                providerId = providerId,
                onDismiss = { showAddModelDialog = false },
                onConfirm = { model ->
                    viewModel.createModel(
                        model = model,
                        onSuccess = {
                            showAddModelDialog = false
                            scope.launch {
                                snackbarHostState.showSnackbar("Model added successfully")
                            }
                        },
                        onError = { error ->
                            scope.launch {
                                snackbarHostState.showSnackbar(error)
                            }
                        }
                    )
                }
            )
        }
    }
    
    // 编辑模型对话框
    editingModel?.let { model ->
        AddModelDialog(
            providerId = model.providerId,
            editingModel = model,
            onDismiss = { editingModel = null },
            onConfirm = { updatedModel ->
                viewModel.updateModel(
                    model = updatedModel,
                    onSuccess = {
                        editingModel = null
                        scope.launch {
                            snackbarHostState.showSnackbar("Model updated successfully")
                        }
                    },
                    onError = { error ->
                        scope.launch {
                            snackbarHostState.showSnackbar(error)
                        }
                    }
                )
            }
        )
    }
}