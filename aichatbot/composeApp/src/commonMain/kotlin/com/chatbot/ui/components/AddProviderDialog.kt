package com.chatbot.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import com.chatbot.data.models.LlmProviderModel
import com.chatbot.data.models.ProviderType
import com.chatbot.theme.LocalShadcnTheme
import kotlinx.datetime.Clock

/**
 * 添加Provider对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddProviderDialog(
    onDismiss: () -> Unit,
    onConfirm: (LlmProviderModel) -> Unit
) {
    val theme = LocalShadcnTheme.current
    
    var name by remember { mutableStateOf("") }
    var selectedType by remember { mutableStateOf(ProviderType.OPENAI) }
    var description by remember { mutableStateOf("") }
    var apiKey by remember { mutableStateOf("") }
    var baseUrl by remember { mutableStateOf("") }
    var proxyUrl by remember { mutableStateOf("") }
    var apiVersion by remember { mutableStateOf("") }
    
    var showApiKey by remember { mutableStateOf(false) }
    var typeDropdownExpanded by remember { mutableStateOf(false) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = theme.popover,
        titleContentColor = theme.popoverForeground,
        textContentColor = theme.popoverForeground,
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    Icons.Default.Add,
                    contentDescription = null,
                    tint = theme.primary
                )
                Text("Add Provider")
            }
        },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Provider Name
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("Provider Name *") },
                    leadingIcon = {
                        Icon(Icons.Default.Label, contentDescription = null)
                    },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = theme.primary,
                        unfocusedBorderColor = theme.border,
                        focusedLabelColor = theme.primary,
                        unfocusedLabelColor = theme.mutedForeground,
                        focusedTextColor = theme.popoverForeground,
                        unfocusedTextColor = theme.popoverForeground
                    )
                )
                
                // Provider Type Dropdown
                ExposedDropdownMenuBox(
                    expanded = typeDropdownExpanded,
                    onExpandedChange = { typeDropdownExpanded = !typeDropdownExpanded }
                ) {
                    OutlinedTextField(
                        value = selectedType.displayName,
                        onValueChange = {},
                        readOnly = true,
                        label = { Text("Provider Type *") },
                        leadingIcon = {
                            Icon(
                                getProviderIcon(selectedType.value),
                                contentDescription = null
                            )
                        },
                        trailingIcon = {
                            ExposedDropdownMenuDefaults.TrailingIcon(expanded = typeDropdownExpanded)
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor(),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = theme.primary,
                            unfocusedBorderColor = theme.border,
                            focusedLabelColor = theme.primary,
                            unfocusedLabelColor = theme.mutedForeground,
                            focusedTextColor = theme.popoverForeground,
                            unfocusedTextColor = theme.popoverForeground
                        )
                    )
                    
                    ExposedDropdownMenu(
                        expanded = typeDropdownExpanded,
                        onDismissRequest = { typeDropdownExpanded = false }
                    ) {
                        ProviderType.entries.forEach { type ->
                            DropdownMenuItem(
                                text = {
                                    Row(
                                        horizontalArrangement = Arrangement.spacedBy(12.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Icon(
                                            getProviderIcon(type.value),
                                            contentDescription = null,
                                            tint = theme.primary
                                        )
                                        Text(type.displayName)
                                    }
                                },
                                onClick = {
                                    selectedType = type
                                    typeDropdownExpanded = false
                                    // Auto-fill base URL
                                    baseUrl = getDefaultBaseUrl(type.value) ?: baseUrl
                                }
                            )
                        }
                    }
                }
                
                // Description
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("Description") },
                    leadingIcon = {
                        Icon(Icons.Default.Description, contentDescription = null)
                    },
                    minLines = 2,
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = theme.primary,
                        unfocusedBorderColor = theme.border,
                        focusedLabelColor = theme.primary,
                        unfocusedLabelColor = theme.mutedForeground,
                        focusedTextColor = theme.popoverForeground,
                        unfocusedTextColor = theme.popoverForeground
                    )
                )
                
                // API Key
                OutlinedTextField(
                    value = apiKey,
                    onValueChange = { apiKey = it },
                    label = { 
                        Text(
                            if (selectedType == ProviderType.OPENAI_COMPATIBLE) 
                                "API Key (Optional)" 
                            else 
                                "API Key *"
                        )
                    },
                    leadingIcon = {
                        Icon(Icons.Default.Key, contentDescription = null)
                    },
                    trailingIcon = {
                        IconButton(
                            onClick = { showApiKey = !showApiKey }
                        ) {
                            Icon(
                                if (showApiKey) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                                contentDescription = if (showApiKey) "Hide" else "Show"
                            )
                        }
                    },
                    visualTransformation = if (showApiKey) VisualTransformation.None else PasswordVisualTransformation(),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = theme.primary,
                        unfocusedBorderColor = theme.border,
                        focusedLabelColor = theme.primary,
                        unfocusedLabelColor = theme.mutedForeground,
                        focusedTextColor = theme.popoverForeground,
                        unfocusedTextColor = theme.popoverForeground
                    )
                )
                
                // Base URL (required for OpenAI Compatible)
                if (selectedType == ProviderType.OPENAI_COMPATIBLE) {
                    OutlinedTextField(
                        value = baseUrl,
                        onValueChange = { baseUrl = it },
                        label = { Text("Base URL *") },
                        placeholder = { Text("https://api.example.com") },
                        leadingIcon = {
                            Icon(Icons.Default.Link, contentDescription = null)
                        },
                        singleLine = true,
                        modifier = Modifier.fillMaxWidth(),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = theme.primary,
                            unfocusedBorderColor = theme.border,
                            focusedLabelColor = theme.primary,
                            unfocusedLabelColor = theme.mutedForeground,
                            focusedTextColor = theme.popoverForeground,
                            unfocusedTextColor = theme.popoverForeground
                        )
                    )
                }
                
                // Proxy URL (optional)
                OutlinedTextField(
                    value = proxyUrl,
                    onValueChange = { proxyUrl = it },
                    label = { Text("Proxy URL (Optional)") },
                    leadingIcon = {
                        Icon(Icons.Default.VpnKey, contentDescription = null)
                    },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = theme.primary,
                        unfocusedBorderColor = theme.border,
                        focusedLabelColor = theme.primary,
                        unfocusedLabelColor = theme.mutedForeground,
                        focusedTextColor = theme.popoverForeground,
                        unfocusedTextColor = theme.popoverForeground
                    )
                )
                
                // API Version (for Anthropic)
                if (selectedType == ProviderType.ANTHROPIC) {
                    OutlinedTextField(
                        value = apiVersion,
                        onValueChange = { apiVersion = it },
                        label = { Text("API Version") },
                        placeholder = { Text("2023-06-01") },
                        leadingIcon = {
                            Icon(Icons.Default.Code, contentDescription = null)
                        },
                        singleLine = true,
                        modifier = Modifier.fillMaxWidth(),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = theme.primary,
                            unfocusedBorderColor = theme.border,
                            focusedLabelColor = theme.primary,
                            unfocusedLabelColor = theme.mutedForeground,
                            focusedTextColor = theme.popoverForeground,
                            unfocusedTextColor = theme.popoverForeground
                        )
                    )
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    val now = Clock.System.now().toEpochMilliseconds()
                    val provider = LlmProviderModel(
                        name = name,
                        providerType = selectedType.value,
                        description = description.ifEmpty { null },
                        apiKey = apiKey.ifEmpty { null },
                        baseUrl = if (selectedType == ProviderType.OPENAI_COMPATIBLE) {
                            baseUrl
                        } else {
                            baseUrl.ifEmpty { getDefaultBaseUrl(selectedType.value) }
                        },
                        proxyUrl = proxyUrl.ifEmpty { null },
                        apiVersion = apiVersion.ifEmpty { null },
                        createdAt = now,
                        updatedAt = now
                    )
                    onConfirm(provider)
                },
                enabled = name.isNotEmpty() && 
                    (selectedType == ProviderType.OPENAI_COMPATIBLE || apiKey.isNotEmpty()) &&
                    (selectedType != ProviderType.OPENAI_COMPATIBLE || baseUrl.isNotEmpty()),
                colors = ButtonDefaults.buttonColors(
                    containerColor = theme.primary,
                    contentColor = theme.primaryForeground
                )
            ) {
                Text("Add")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = theme.mutedForeground
                )
            ) {
                Text("Cancel")
            }
        }
    )
}