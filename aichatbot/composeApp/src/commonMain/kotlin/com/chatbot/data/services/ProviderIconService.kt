package com.chatbot.data.services

import co.touchlab.kermit.Logger
import com.russhwolf.settings.Settings
import com.russhwolf.settings.get
import com.russhwolf.settings.set

/**
 * Provider图标服务
 * 管理Provider的图标存储和获取
 */
class ProviderIconService(
    private val settings: Settings = Settings()
) {
    companion object {
        private const val ICON_PREFIX = "provider_icon_"
        
        // 默认图标（使用Material Icons名称）
        private val DEFAULT_ICONS = mapOf(
            "openai" to "smart_toy",
            "gemini" to "auto_awesome",
            "anthropic" to "psychology",
            "openai_compatible" to "extension"
        )
    }
    
    /**
     * 获取提供商图标
     */
    fun getProviderIcon(providerType: String): String {
        // 先从设置中查找自定义图标
        val customIcon: String? = settings[ICON_PREFIX + providerType]
        if (!customIcon.isNullOrEmpty()) {
            return customIcon
        }
        
        // 返回默认图标
        return DEFAULT_ICONS[providerType] ?: "smart_toy"
    }
    
    /**
     * 保存提供商图标
     */
    fun saveProviderIcon(providerType: String, icon: String) {
        settings[ICON_PREFIX + providerType] = icon
        Logger.d { "Saved icon for provider $providerType: $icon" }
    }
    
    /**
     * 删除提供商图标
     */
    fun deleteProviderIcon(providerType: String) {
        settings.remove(ICON_PREFIX + providerType)
        Logger.d { "Deleted icon for provider $providerType" }
    }
    
    /**
     * 获取所有自定义图标
     */
    fun getAllCustomIcons(): Map<String, String> {
        val icons = mutableMapOf<String, String>()
        settings.keys.forEach { key ->
            if (key.startsWith(ICON_PREFIX)) {
                val providerType = key.removePrefix(ICON_PREFIX)
                val icon: String? = settings[key]
                if (!icon.isNullOrEmpty()) {
                    icons[providerType] = icon
                }
            }
        }
        return icons
    }
    
    /**
     * 清除所有自定义图标
     */
    fun clearAllCustomIcons() {
        settings.keys.filter { it.startsWith(ICON_PREFIX) }.forEach { key ->
            settings.remove(key)
        }
        Logger.d { "Cleared all custom provider icons" }
    }
    
    /**
     * 初始化服务
     */
    suspend fun initialize() {
        Logger.d { "ProviderIconService initialized" }
    }
}