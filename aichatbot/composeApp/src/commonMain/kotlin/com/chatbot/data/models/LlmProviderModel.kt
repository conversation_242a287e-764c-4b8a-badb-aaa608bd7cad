package com.chatbot.data.models

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

/**
 * LLM Provider Model - 数据模型类
 *
 * 纯数据模型，只包含属性和序列化方法，不包含业务逻辑
 */
@Serializable
data class LlmProviderModel(
    val id: Int? = null,
    val name: String,
    val providerType: String,
    val description: String? = null,
    val icon: String? = null,
    val apiKey: String? = null,
    val baseUrl: String? = null,
    val proxyUrl: String? = null,
    val apiVersion: String? = null,
    val timeoutSeconds: Int = 60,
    val maxRetries: Int = 3,
    val customHeaders: Map<String, String>? = null,
    val isActive: Boolean = true,
    val createdAt: Long,
    val updatedAt: Long
) {
    companion object {
        const val OPENAI = "openai"
        const val OPENAI_COMPATIBLE = "openai_compatible"
        const val GEMINI = "gemini"
        const val ANTHROPIC = "anthropic"
        
        val supportedTypes = listOf(
            OPENAI,
            OPENAI_COMPATIBLE,
            GEMINI,
            ANTHROPIC
        )
    }
    
    fun copyWithUpdate(
        name: String? = null,
        description: String? = null,
        icon: String? = null,
        apiKey: String? = null,
        baseUrl: String? = null,
        proxyUrl: String? = null,
        apiVersion: String? = null,
        timeoutSeconds: Int? = null,
        maxRetries: Int? = null,
        customHeaders: Map<String, String>? = null,
        isActive: Boolean? = null
    ): LlmProviderModel {
        return copy(
            name = name ?: this.name,
            description = description ?: this.description,
            icon = icon ?: this.icon,
            apiKey = apiKey ?: this.apiKey,
            baseUrl = baseUrl ?: this.baseUrl,
            proxyUrl = proxyUrl ?: this.proxyUrl,
            apiVersion = apiVersion ?: this.apiVersion,
            timeoutSeconds = timeoutSeconds ?: this.timeoutSeconds,
            maxRetries = maxRetries ?: this.maxRetries,
            customHeaders = customHeaders ?: this.customHeaders,
            isActive = isActive ?: this.isActive,
            updatedAt = kotlinx.datetime.Clock.System.now().toEpochMilliseconds()
        )
    }
}

enum class ProviderType(val value: String, val displayName: String) {
    OPENAI("openai", "OpenAI"),
    OPENAI_COMPATIBLE("openai_compatible", "OpenAI Compatible"),
    GEMINI("gemini", "Google Gemini"),
    ANTHROPIC("anthropic", "Anthropic");
    
    companion object {
        fun fromValue(value: String): ProviderType? {
            return entries.find { it.value == value }
        }
    }
}