package com.chatbot.data.repositories

import com.chatbot.data.database.DatabaseService
import com.chatbot.data.models.LlmProviderModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

class LlmProviderRepository(
    private val databaseService: DatabaseService
) {
    private val _providers = MutableStateFlow<List<LlmProviderModel>>(emptyList())
    val providers: Flow<List<LlmProviderModel>> = _providers.asStateFlow()
    
    suspend fun loadProviders() {
        _providers.value = databaseService.getAllProviders()
    }
    
    suspend fun getAllProviders(): List<LlmProviderModel> {
        val providers = databaseService.getAllProviders()
        _providers.value = providers
        return providers
    }
    
    suspend fun getProviderById(id: Int): LlmProviderModel? {
        return databaseService.getProviderById(id)
    }
    
    suspend fun createProvider(provider: LlmProviderModel): LlmProviderModel {
        val createdProvider = databaseService.insertProvider(provider)
        loadProviders() // Reload to update the flow
        return createdProvider
    }
    
    suspend fun updateProvider(provider: LlmProviderModel): Boolean {
        val success = databaseService.updateProvider(provider)
        if (success) {
            loadProviders() // Reload to update the flow
        }
        return success
    }
    
    suspend fun deleteProvider(id: Int): Boolean {
        val success = databaseService.deleteProvider(id)
        if (success) {
            loadProviders() // Reload to update the flow
        }
        return success
    }
    
    suspend fun toggleProviderStatus(provider: LlmProviderModel): Boolean {
        val updatedProvider = provider.copy(
            isActive = !provider.isActive,
            updatedAt = kotlinx.datetime.Clock.System.now().toEpochMilliseconds()
        )
        return updateProvider(updatedProvider)
    }
    
    suspend fun getActiveProviders(): List<LlmProviderModel> {
        return getAllProviders().filter { it.isActive }
    }
    
    suspend fun getProvidersByType(type: String): List<LlmProviderModel> {
        return getAllProviders().filter { it.providerType == type }
    }
}