package com.chatbot.data.repositories

import com.chatbot.data.database.DatabaseService
import com.chatbot.data.models.LlmModel
import com.chatbot.data.models.ModelCapability
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

class LlmModelRepository(
    private val databaseService: DatabaseService
) {
    private val _models = MutableStateFlow<List<LlmModel>>(emptyList())
    val models: Flow<List<LlmModel>> = _models.asStateFlow()
    
    suspend fun loadModels() {
        _models.value = databaseService.getAllModels()
    }
    
    suspend fun getAllModels(): List<LlmModel> {
        val models = databaseService.getAllModels()
        _models.value = models
        return models
    }
    
    suspend fun getModelsByProviderId(providerId: Int): List<LlmModel> {
        return databaseService.getModelsByProviderId(providerId)
    }
    
    suspend fun createModel(model: LlmModel): LlmModel {
        val createdModel = databaseService.insertModel(model)
        loadModels() // Reload to update the flow
        return createdModel
    }
    
    suspend fun updateModel(model: LlmModel): Boolean {
        val success = databaseService.updateModel(model)
        if (success) {
            loadModels() // Reload to update the flow
        }
        return success
    }
    
    suspend fun deleteModel(id: Int): Boolean {
        val success = databaseService.deleteModel(id)
        if (success) {
            loadModels() // Reload to update the flow
        }
        return success
    }
    
    suspend fun deleteModelsByProviderId(providerId: Int): Boolean {
        val success = databaseService.deleteModelsByProviderId(providerId)
        if (success) {
            loadModels() // Reload to update the flow
        }
        return success
    }
    
    suspend fun toggleModelStatus(model: LlmModel): Boolean {
        val updatedModel = model.copy(
            isActive = !model.isActive,
            updatedAt = kotlinx.datetime.Clock.System.now().toEpochMilliseconds()
        )
        return updateModel(updatedModel)
    }
    
    suspend fun getActiveModels(): List<LlmModel> {
        return getAllModels().filter { it.isActive }
    }
    
    suspend fun getActiveModelsByProviderId(providerId: Int): List<LlmModel> {
        return getModelsByProviderId(providerId).filter { it.isActive }
    }
    
    suspend fun getModelsWithCapability(capability: ModelCapability): List<LlmModel> {
        return getAllModels().filter { it.capabilities.contains(capability) }
    }
    
    suspend fun searchModels(query: String): List<LlmModel> {
        val lowerQuery = query.lowercase()
        return getAllModels().filter { model ->
            model.name.lowercase().contains(lowerQuery) ||
            model.description?.lowercase()?.contains(lowerQuery) == true ||
            model.family?.lowercase()?.contains(lowerQuery) == true
        }
    }
}