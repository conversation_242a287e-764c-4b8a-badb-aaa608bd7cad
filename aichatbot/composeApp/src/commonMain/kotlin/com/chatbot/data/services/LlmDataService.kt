package com.chatbot.data.services

import com.chatbot.data.database.DatabaseService
import com.chatbot.data.models.LlmModel
import com.chatbot.data.models.LlmProviderModel
import com.chatbot.data.models.ModelCapability
import com.chatbot.data.repositories.LlmModelRepository
import com.chatbot.data.repositories.LlmProviderRepository
import kotlinx.datetime.Clock

/**
 * LLM 数据服务 - 业务逻辑层
 *
 * 封装Repository，提供高层级的业务方法
 */
class LlmDataService(
    databaseService: DatabaseService,
    private val iconService: ProviderIconService
) {
    private val providerRepository = LlmProviderRepository(databaseService)
    private val modelRepository = LlmModelRepository(databaseService)
    
    // ========== 提供商相关方法 ==========
    
    /**
     * 获取所有提供商
     */
    suspend fun getAllProviders(): List<LlmProviderModel> {
        return providerRepository.getAllProviders()
    }
    
    /**
     * 获取所有活跃提供商
     */
    suspend fun getActiveProviders(): List<LlmProviderModel> {
        return providerRepository.getActiveProviders()
    }
    
    /**
     * 根据ID获取提供商
     */
    suspend fun getProviderById(id: Int): LlmProviderModel? {
        return providerRepository.getProviderById(id)
    }
    
    /**
     * 根据名称获取提供商
     */
    suspend fun getProviderByName(name: String): LlmProviderModel? {
        return getAllProviders().find { it.name == name }
    }
    
    /**
     * 按类型获取提供商
     */
    suspend fun getProvidersByType(providerType: String): List<LlmProviderModel> {
        return providerRepository.getProvidersByType(providerType)
    }
    
    /**
     * 保存提供商（创建或更新）
     */
    suspend fun saveProvider(provider: LlmProviderModel): LlmProviderModel? {
        return if (provider.id == null) {
            // Create new provider
            val newProvider = provider.copy(
                createdAt = Clock.System.now().toEpochMilliseconds(),
                updatedAt = Clock.System.now().toEpochMilliseconds()
            )
            providerRepository.createProvider(newProvider)
        } else {
            // Update existing provider
            val updatedProvider = provider.copy(
                updatedAt = Clock.System.now().toEpochMilliseconds()
            )
            if (providerRepository.updateProvider(updatedProvider)) {
                updatedProvider
            } else {
                null
            }
        }
    }
    
    /**
     * 删除提供商（包括清理图标文件）
     */
    suspend fun deleteProvider(providerId: Int): Boolean {
        // 先获取提供商信息
        val provider = providerRepository.getProviderById(providerId)
        
        // 删除相关的模型
        modelRepository.deleteModelsByProviderId(providerId)
        
        // 删除提供商记录
        val deleted = providerRepository.deleteProvider(providerId)
        
        // 如果删除成功且有图标，清理图标文件
        if (deleted && provider?.icon != null) {
            try {
                iconService.deleteProviderIcon(provider.icon)
            } catch (e: Exception) {
                // 图标删除失败不影响主要操作
                co.touchlab.kermit.Logger.w { "Failed to delete provider icon: $e" }
            }
        }
        
        return deleted
    }
    
    /**
     * 切换提供商状态
     */
    suspend fun toggleProviderStatus(provider: LlmProviderModel): Boolean {
        return providerRepository.toggleProviderStatus(provider)
    }
    
    // ========== 模型相关方法 ==========
    
    /**
     * 获取所有模型
     */
    suspend fun getAllModels(): List<LlmModel> {
        return modelRepository.getAllModels()
    }
    
    /**
     * 根据模型名称获取模型
     */
    suspend fun getModelByName(modelName: String): LlmModel? {
        return getAllModels().find { it.name == modelName }
    }
    
    /**
     * 根据提供商ID获取模型
     */
    suspend fun getModelsByProviderId(providerId: Int): List<LlmModel> {
        return modelRepository.getModelsByProviderId(providerId)
    }
    
    /**
     * 获取活跃模型
     */
    suspend fun getActiveModels(): List<LlmModel> {
        return modelRepository.getActiveModels()
    }
    
    /**
     * 根据提供商ID获取活跃模型
     */
    suspend fun getActiveModelsByProviderId(providerId: Int): List<LlmModel> {
        return modelRepository.getActiveModelsByProviderId(providerId)
    }
    
    /**
     * 保存模型（创建或更新）
     */
    suspend fun saveModel(model: LlmModel): LlmModel? {
        return if (model.id == null) {
            // Create new model
            val newModel = model.copy(
                createdAt = Clock.System.now().toEpochMilliseconds(),
                updatedAt = Clock.System.now().toEpochMilliseconds()
            )
            modelRepository.createModel(newModel)
        } else {
            // Update existing model
            val updatedModel = model.copy(
                updatedAt = Clock.System.now().toEpochMilliseconds()
            )
            if (modelRepository.updateModel(updatedModel)) {
                updatedModel
            } else {
                null
            }
        }
    }
    
    /**
     * 删除模型
     */
    suspend fun deleteModel(modelId: Int): Boolean {
        return modelRepository.deleteModel(modelId)
    }
    
    /**
     * 切换模型状态
     */
    suspend fun toggleModelStatus(model: LlmModel): Boolean {
        return modelRepository.toggleModelStatus(model)
    }
    
    /**
     * 根据能力获取模型
     */
    suspend fun getModelsWithCapability(capability: ModelCapability): List<LlmModel> {
        return modelRepository.getModelsWithCapability(capability)
    }
    
    /**
     * 搜索模型
     */
    suspend fun searchModels(query: String): List<LlmModel> {
        return modelRepository.searchModels(query)
    }
    
    /**
     * 批量导入模型
     */
    suspend fun importModels(providerId: Int, models: List<LlmModel>): List<LlmModel> {
        val importedModels = mutableListOf<LlmModel>()
        for (model in models) {
            val modelToImport = model.copy(
                providerId = providerId,
                createdAt = Clock.System.now().toEpochMilliseconds(),
                updatedAt = Clock.System.now().toEpochMilliseconds()
            )
            val imported = modelRepository.createModel(modelToImport)
            importedModels.add(imported)
        }
        return importedModels
    }
    
    // ========== 组合业务方法 ==========
    
    /**
     * 获取提供商及其模型
     */
    suspend fun getProviderWithModels(providerId: Int): Pair<LlmProviderModel?, List<LlmModel>> {
        val provider = providerRepository.getProviderById(providerId)
        val models = modelRepository.getModelsByProviderId(providerId)
        return provider to models
    }
    
    /**
     * 获取所有活跃的提供商和模型
     */
    suspend fun getActiveProvidersWithModels(): Map<LlmProviderModel, List<LlmModel>> {
        val activeProviders = getActiveProviders()
        val result = mutableMapOf<LlmProviderModel, List<LlmModel>>()
        
        for (provider in activeProviders) {
            provider.id?.let { providerId ->
                val activeModels = modelRepository.getActiveModelsByProviderId(providerId)
                result[provider] = activeModels
            }
        }
        
        return result
    }
    
    /**
     * 克隆提供商（包括其模型）
     */
    suspend fun cloneProvider(providerId: Int, newName: String): LlmProviderModel? {
        val sourceProvider = providerRepository.getProviderById(providerId) ?: return null
        val sourceModels = modelRepository.getModelsByProviderId(providerId)
        
        // 创建新的提供商
        val newProvider = sourceProvider.copy(
            id = null,
            name = newName,
            createdAt = Clock.System.now().toEpochMilliseconds(),
            updatedAt = Clock.System.now().toEpochMilliseconds()
        )
        
        val createdProvider = providerRepository.createProvider(newProvider)
        
        // 复制所有模型到新提供商
        createdProvider.id?.let { newProviderId ->
            for (model in sourceModels) {
                val newModel = model.copy(
                    id = null,
                    providerId = newProviderId,
                    createdAt = Clock.System.now().toEpochMilliseconds(),
                    updatedAt = Clock.System.now().toEpochMilliseconds()
                )
                modelRepository.createModel(newModel)
            }
        }
        
        return createdProvider
    }
}