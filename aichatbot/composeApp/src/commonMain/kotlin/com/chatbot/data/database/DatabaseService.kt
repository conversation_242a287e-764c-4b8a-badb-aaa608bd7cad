package com.chatbot.data.database

import app.cash.sqldelight.db.SqlDriver
import com.chatbot.data.models.LlmModel
import com.chatbot.data.models.LlmProviderModel
import com.chatbot.data.models.ModelCapability
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.builtins.SetSerializer
import kotlinx.serialization.builtins.serializer

class DatabaseService(driver: SqlDriver) {
    private val database = AppDatabase(driver)
    private val queries = database.appDatabaseQueries
    
    private val json = Json { 
        ignoreUnknownKeys = true
        isLenient = true
    }
    
    // Provider operations
    suspend fun getAllProviders(): List<LlmProviderModel> = withContext(Dispatchers.IO) {
        queries.getAllProviders().executeAsList().map { row ->
            LlmProviderModel(
                id = row.id?.toInt(),
                name = row.name,
                providerType = row.provider_type,
                description = row.description,
                icon = row.icon,
                apiKey = row.api_key,
                baseUrl = row.base_url,
                proxyUrl = row.proxy_url,
                apiVersion = row.api_version,
                timeoutSeconds = row.timeout_seconds.toInt(),
                maxRetries = row.max_retries.toInt(),
                customHeaders = row.custom_headers?.let { 
                    json.decodeFromString<Map<String, String>>(it) 
                },
                isActive = row.is_active == 1L,
                createdAt = row.created_at,
                updatedAt = row.updated_at
            )
        }
    }
    
    suspend fun getProviderById(id: Int): LlmProviderModel? = withContext(Dispatchers.IO) {
        queries.getProviderById(id.toLong()).executeAsOneOrNull()?.let { row ->
            LlmProviderModel(
                id = row.id?.toInt(),
                name = row.name,
                providerType = row.provider_type,
                description = row.description,
                icon = row.icon,
                apiKey = row.api_key,
                baseUrl = row.base_url,
                proxyUrl = row.proxy_url,
                apiVersion = row.api_version,
                timeoutSeconds = row.timeout_seconds.toInt(),
                maxRetries = row.max_retries.toInt(),
                customHeaders = row.custom_headers?.let { 
                    json.decodeFromString<Map<String, String>>(it) 
                },
                isActive = row.is_active == 1L,
                createdAt = row.created_at,
                updatedAt = row.updated_at
            )
        }
    }
    
    suspend fun insertProvider(provider: LlmProviderModel): LlmProviderModel = withContext(Dispatchers.IO) {
        queries.insertProvider(
            name = provider.name,
            provider_type = provider.providerType,
            description = provider.description,
            icon = provider.icon,
            api_key = provider.apiKey,
            base_url = provider.baseUrl,
            proxy_url = provider.proxyUrl,
            api_version = provider.apiVersion,
            timeout_seconds = provider.timeoutSeconds.toLong(),
            max_retries = provider.maxRetries.toLong(),
            custom_headers = provider.customHeaders?.let { json.encodeToString(it) },
            is_active = if (provider.isActive) 1L else 0L,
            created_at = provider.createdAt,
            updated_at = provider.updatedAt
        )
        
        val id = queries.getLastInsertedProviderId().executeAsOne().toInt()
        provider.copy(id = id)
    }
    
    suspend fun updateProvider(provider: LlmProviderModel): Boolean = withContext(Dispatchers.IO) {
        if (provider.id == null) return@withContext false
        
        queries.updateProvider(
            name = provider.name,
            provider_type = provider.providerType,
            description = provider.description,
            icon = provider.icon,
            api_key = provider.apiKey,
            base_url = provider.baseUrl,
            proxy_url = provider.proxyUrl,
            api_version = provider.apiVersion,
            timeout_seconds = provider.timeoutSeconds.toLong(),
            max_retries = provider.maxRetries.toLong(),
            custom_headers = provider.customHeaders?.let { json.encodeToString(it) },
            is_active = if (provider.isActive) 1L else 0L,
            updated_at = provider.updatedAt,
            id = provider.id.toLong()
        )
        true
    }
    
    suspend fun deleteProvider(id: Int): Boolean = withContext(Dispatchers.IO) {
        queries.deleteProvider(id.toLong())
        true
    }
    
    // Model operations
    suspend fun getAllModels(): List<LlmModel> = withContext(Dispatchers.IO) {
        queries.getAllModels().executeAsList().map { row ->
            LlmModel(
                id = row.id?.toInt(),
                providerId = row.provider_id.toInt(),
                name = row.name,
                version = row.version,
                description = row.description,
                family = row.family,
                releaseDate = row.release_date,
                capabilities = row.capabilities?.let {
                    json.decodeFromString<Set<ModelCapability>>(it)
                } ?: emptySet(),
                inputTokens = row.input_tokens?.toInt(),
                outputTokens = row.output_tokens?.toInt(),
                totalTokens = row.total_tokens?.toInt(),
                maxTokens = row.max_tokens?.toInt() ?: 128000,
                maxInputTokens = row.max_input_tokens?.toInt(),
                maxOutputTokens = row.max_output_tokens?.toInt(),
                supportedLanguages = row.supported_languages?.let {
                    json.decodeFromString<Set<String>>(it)
                } ?: emptySet(),
                pricingTier = row.pricing_tier,
                pricingInput = row.pricing_input,
                pricingOutput = row.pricing_output,
                currency = row.currency ?: "USD",
                isActive = row.is_active == 1L,
                createdAt = row.created_at,
                updatedAt = row.updated_at
            )
        }
    }
    
    suspend fun getModelsByProviderId(providerId: Int): List<LlmModel> = withContext(Dispatchers.IO) {
        queries.getModelsByProviderId(providerId.toLong()).executeAsList().map { row ->
            LlmModel(
                id = row.id?.toInt(),
                providerId = row.provider_id.toInt(),
                name = row.name,
                version = row.version,
                description = row.description,
                family = row.family,
                releaseDate = row.release_date,
                capabilities = row.capabilities?.let {
                    json.decodeFromString<Set<ModelCapability>>(it)
                } ?: emptySet(),
                inputTokens = row.input_tokens?.toInt(),
                outputTokens = row.output_tokens?.toInt(),
                totalTokens = row.total_tokens?.toInt(),
                maxTokens = row.max_tokens?.toInt() ?: 128000,
                maxInputTokens = row.max_input_tokens?.toInt(),
                maxOutputTokens = row.max_output_tokens?.toInt(),
                supportedLanguages = row.supported_languages?.let {
                    json.decodeFromString<Set<String>>(it)
                } ?: emptySet(),
                pricingTier = row.pricing_tier,
                pricingInput = row.pricing_input,
                pricingOutput = row.pricing_output,
                currency = row.currency ?: "USD",
                isActive = row.is_active == 1L,
                createdAt = row.created_at,
                updatedAt = row.updated_at
            )
        }
    }
    
    suspend fun insertModel(model: LlmModel): LlmModel = withContext(Dispatchers.IO) {
        queries.insertModel(
            provider_id = model.providerId.toLong(),
            name = model.name,
            version = model.version,
            description = model.description,
            family = model.family,
            release_date = model.releaseDate,
            capabilities = json.encodeToString(SetSerializer(ModelCapability.serializer()), model.capabilities),
            input_tokens = model.inputTokens?.toLong(),
            output_tokens = model.outputTokens?.toLong(),
            total_tokens = model.totalTokens?.toLong(),
            max_tokens = model.maxTokens.toLong(),
            max_input_tokens = model.maxInputTokens?.toLong(),
            max_output_tokens = model.maxOutputTokens?.toLong(),
            supported_languages = json.encodeToString(SetSerializer(String.serializer()), model.supportedLanguages),
            pricing_tier = model.pricingTier,
            pricing_input = model.pricingInput,
            pricing_output = model.pricingOutput,
            currency = model.currency,
            is_active = if (model.isActive) 1L else 0L,
            created_at = model.createdAt,
            updated_at = model.updatedAt
        )
        
        val id = queries.getLastInsertedModelId().executeAsOne().toInt()
        model.copy(id = id)
    }
    
    suspend fun updateModel(model: LlmModel): Boolean = withContext(Dispatchers.IO) {
        if (model.id == null) return@withContext false
        
        queries.updateModel(
            provider_id = model.providerId.toLong(),
            name = model.name,
            version = model.version,
            description = model.description,
            family = model.family,
            release_date = model.releaseDate,
            capabilities = json.encodeToString(SetSerializer(ModelCapability.serializer()), model.capabilities),
            input_tokens = model.inputTokens?.toLong(),
            output_tokens = model.outputTokens?.toLong(),
            total_tokens = model.totalTokens?.toLong(),
            max_tokens = model.maxTokens.toLong(),
            max_input_tokens = model.maxInputTokens?.toLong(),
            max_output_tokens = model.maxOutputTokens?.toLong(),
            supported_languages = json.encodeToString(SetSerializer(String.serializer()), model.supportedLanguages),
            pricing_tier = model.pricingTier,
            pricing_input = model.pricingInput,
            pricing_output = model.pricingOutput,
            currency = model.currency,
            is_active = if (model.isActive) 1L else 0L,
            updated_at = model.updatedAt,
            id = model.id.toLong()
        )
        true
    }
    
    suspend fun deleteModel(id: Int): Boolean = withContext(Dispatchers.IO) {
        queries.deleteModel(id.toLong())
        true
    }
    
    suspend fun deleteModelsByProviderId(providerId: Int): Boolean = withContext(Dispatchers.IO) {
        queries.deleteModelsByProviderId(providerId.toLong())
        true
    }
}