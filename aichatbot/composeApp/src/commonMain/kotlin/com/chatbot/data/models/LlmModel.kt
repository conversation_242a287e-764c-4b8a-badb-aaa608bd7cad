package com.chatbot.data.models

import kotlinx.serialization.Serializable

/**
 * LLM Model - 数据模型类
 *
 * 纯数据模型，只包含属性和序列化方法，不包含业务逻辑
 */
@Serializable
data class LlmModel(
    val id: Int? = null,
    val providerId: Int,
    val name: String,
    val version: String? = null,
    val description: String? = null,
    val family: String? = null,
    val releaseDate: String? = null,
    val capabilities: Set<ModelCapability> = emptySet(),
    val inputTokens: Int? = null,
    val outputTokens: Int? = null,
    val totalTokens: Int? = null,
    val maxTokens: Int = 128000,
    val maxInputTokens: Int? = null,
    val maxOutputTokens: Int? = null,
    val supportedLanguages: Set<String> = emptySet(),
    val pricingTier: String? = null,
    val pricingInput: Double? = null,
    val pricingOutput: Double? = null,
    val currency: String = "USD",
    val isActive: Boolean = true,
    val createdAt: Long,
    val updatedAt: Long
) {
    fun copyWithUpdate(
        name: String? = null,
        version: String? = null,
        description: String? = null,
        capabilities: Set<ModelCapability>? = null,
        maxTokens: Int? = null,
        pricingInput: Double? = null,
        pricingOutput: Double? = null,
        isActive: Boolean? = null
    ): LlmModel {
        return copy(
            name = name ?: this.name,
            version = version ?: this.version,
            description = description ?: this.description,
            capabilities = capabilities ?: this.capabilities,
            maxTokens = maxTokens ?: this.maxTokens,
            pricingInput = pricingInput ?: this.pricingInput,
            pricingOutput = pricingOutput ?: this.pricingOutput,
            isActive = isActive ?: this.isActive,
            updatedAt = kotlinx.datetime.Clock.System.now().toEpochMilliseconds()
        )
    }
}

@Serializable
enum class ModelCapability(val displayName: String, val description: String) {
    CHAT("Chat", "Basic chat capabilities"),
    COMPLETION("Completion", "Text completion"),
    EMBEDDING("Embedding", "Generate embeddings"),
    VISION("Vision", "Process images"),
    FUNCTION_CALLING("Function Calling", "Call external functions"),
    STREAMING("Streaming", "Stream responses"),
    JSON_MODE("JSON Mode", "Structured JSON output"),
    CODE_EXECUTION("Code Execution", "Execute code"),
    WEB_SEARCH("Web Search", "Search the web"),
    FILE_UPLOAD("File Upload", "Process uploaded files"),
    VOICE("Voice", "Voice input/output"),
    VIDEO("Video", "Process video content");
    
    companion object {
        fun fromString(value: String): ModelCapability? {
            return entries.find { 
                it.name.equals(value, ignoreCase = true) || 
                it.displayName.equals(value, ignoreCase = true)
            }
        }
    }
}

@Serializable
data class ModelPricing(
    val inputPricePerMillion: Double,
    val outputPricePerMillion: Double,
    val currency: String = "USD",
    val tier: PricingTier = PricingTier.STANDARD
)

@Serializable
enum class PricingTier {
    FREE,
    STANDARD,
    PREMIUM,
    ENTERPRISE
}