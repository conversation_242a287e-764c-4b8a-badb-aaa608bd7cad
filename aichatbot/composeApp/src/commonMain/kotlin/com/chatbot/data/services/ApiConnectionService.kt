package com.chatbot.data.services

import co.touchlab.kermit.Logger
import com.chatbot.data.models.LlmProviderModel
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.plugins.logging.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

/**
 * API连接测试服务
 */
class ApiConnectionService {
    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }
    
    private val client = HttpClient {
        install(ContentNegotiation) {
            json(json)
        }
        
        install(Logging) {
            logger = object : io.ktor.client.plugins.logging.Logger {
                override fun log(message: String) {
                    Logger.d { message }
                }
            }
            level = LogLevel.INFO
        }
        
        install(HttpTimeout) {
            requestTimeoutMillis = 30000
            connectTimeoutMillis = 10000
            socketTimeoutMillis = 30000
        }
    }
    
    /**
     * 测试API连接
     */
    suspend fun testConnection(provider: LlmProviderModel): ConnectionTestResult {
        return try {
            when (provider.providerType) {
                LlmProviderModel.OPENAI -> testOpenAIConnection(provider)
                LlmProviderModel.OPENAI_COMPATIBLE -> testOpenAICompatibleConnection(provider)
                LlmProviderModel.GEMINI -> testGeminiConnection(provider)
                LlmProviderModel.ANTHROPIC -> testAnthropicConnection(provider)
                else -> ConnectionTestResult(
                    success = false,
                    message = "Unsupported provider type: ${provider.providerType}"
                )
            }
        } catch (e: Exception) {
            Logger.e(e) { "Connection test failed" }
            ConnectionTestResult(
                success = false,
                message = "Connection failed: ${e.message}"
            )
        }
    }
    
    private suspend fun testOpenAIConnection(provider: LlmProviderModel): ConnectionTestResult {
        if (provider.apiKey.isNullOrEmpty()) {
            return ConnectionTestResult(
                success = false,
                message = "API key is required"
            )
        }
        
        val baseUrl = provider.baseUrl ?: "https://api.openai.com"
        val url = "$baseUrl/v1/models"
        
        return try {
            val response: HttpResponse = client.get(url) {
                headers {
                    append(HttpHeaders.Authorization, "Bearer ${provider.apiKey}")
                    provider.customHeaders?.forEach { (key, value) ->
                        append(key, value)
                    }
                }
                
                if (!provider.proxyUrl.isNullOrEmpty()) {
                    // Note: Proxy configuration would need platform-specific implementation
                    Logger.w { "Proxy configuration not yet implemented" }
                }
            }
            
            if (response.status.isSuccess()) {
                ConnectionTestResult(
                    success = true,
                    message = "Connection successful",
                    models = parseOpenAIModels(response.bodyAsText())
                )
            } else {
                ConnectionTestResult(
                    success = false,
                    message = "API returned ${response.status.value}: ${response.status.description}"
                )
            }
        } catch (e: Exception) {
            ConnectionTestResult(
                success = false,
                message = "Connection failed: ${e.message}"
            )
        }
    }
    
    private suspend fun testOpenAICompatibleConnection(provider: LlmProviderModel): ConnectionTestResult {
        if (provider.baseUrl.isNullOrEmpty()) {
            return ConnectionTestResult(
                success = false,
                message = "Base URL is required for OpenAI-compatible providers"
            )
        }
        
        // Same as OpenAI but with custom base URL
        return testOpenAIConnection(provider)
    }
    
    private suspend fun testGeminiConnection(provider: LlmProviderModel): ConnectionTestResult {
        if (provider.apiKey.isNullOrEmpty()) {
            return ConnectionTestResult(
                success = false,
                message = "API key is required"
            )
        }
        
        val baseUrl = provider.baseUrl ?: "https://generativelanguage.googleapis.com"
        val url = "$baseUrl/v1beta/models?key=${provider.apiKey}"
        
        return try {
            val response: HttpResponse = client.get(url) {
                provider.customHeaders?.forEach { (key, value) ->
                    headers.append(key, value)
                }
            }
            
            if (response.status.isSuccess()) {
                ConnectionTestResult(
                    success = true,
                    message = "Connection successful",
                    models = parseGeminiModels(response.bodyAsText())
                )
            } else {
                ConnectionTestResult(
                    success = false,
                    message = "API returned ${response.status.value}: ${response.status.description}"
                )
            }
        } catch (e: Exception) {
            ConnectionTestResult(
                success = false,
                message = "Connection failed: ${e.message}"
            )
        }
    }
    
    private suspend fun testAnthropicConnection(provider: LlmProviderModel): ConnectionTestResult {
        if (provider.apiKey.isNullOrEmpty()) {
            return ConnectionTestResult(
                success = false,
                message = "API key is required"
            )
        }
        
        val baseUrl = provider.baseUrl ?: "https://api.anthropic.com"
        
        // Anthropic doesn't have a models endpoint, so we test with a simple completion
        val url = "$baseUrl/v1/messages"
        
        return try {
            val response: HttpResponse = client.post(url) {
                headers {
                    append("x-api-key", provider.apiKey)
                    append("anthropic-version", provider.apiVersion ?: "2023-06-01")
                    append(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    provider.customHeaders?.forEach { (key, value) ->
                        append(key, value)
                    }
                }
                
                setBody("""
                    {
                        "model": "claude-3-haiku-20240307",
                        "messages": [{"role": "user", "content": "Hi"}],
                        "max_tokens": 1
                    }
                """.trimIndent())
            }
            
            // For Anthropic, we consider 200 or 401 as successful connection
            // (401 means API key is invalid but connection works)
            if (response.status.value in listOf(200, 401)) {
                ConnectionTestResult(
                    success = true,
                    message = if (response.status.value == 200) 
                        "Connection successful" 
                    else 
                        "Connection successful but API key may be invalid",
                    models = listOf("claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307")
                )
            } else {
                ConnectionTestResult(
                    success = false,
                    message = "API returned ${response.status.value}: ${response.status.description}"
                )
            }
        } catch (e: Exception) {
            ConnectionTestResult(
                success = false,
                message = "Connection failed: ${e.message}"
            )
        }
    }
    
    private fun parseOpenAIModels(responseBody: String): List<String> {
        return try {
            val response = json.decodeFromString<OpenAIModelsResponse>(responseBody)
            response.data.map { it.id }
        } catch (e: Exception) {
            Logger.e(e) { "Failed to parse OpenAI models" }
            emptyList()
        }
    }
    
    private fun parseGeminiModels(responseBody: String): List<String> {
        return try {
            val response = json.decodeFromString<GeminiModelsResponse>(responseBody)
            response.models.map { it.name.substringAfterLast("/") }
        } catch (e: Exception) {
            Logger.e(e) { "Failed to parse Gemini models" }
            emptyList()
        }
    }
    
    fun close() {
        client.close()
    }
}

@Serializable
data class ConnectionTestResult(
    val success: Boolean,
    val message: String,
    val models: List<String> = emptyList()
)

@Serializable
private data class OpenAIModelsResponse(
    val data: List<OpenAIModel>
)

@Serializable
private data class OpenAIModel(
    val id: String
)

@Serializable
private data class GeminiModelsResponse(
    val models: List<GeminiModel>
)

@Serializable
private data class GeminiModel(
    val name: String
)