package com.chatbot.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.luminance
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.chatbot.theme.tailwind.TwRadius
import com.chatbot.theme.tailwind.TwTypography

object ShadcnThemeBuilder {
    fun createColorScheme(theme: ShadcnTheme): ColorScheme {
        val isDark = theme.background.luminance() < 0.5
        
        return if (isDark) {
            darkColorScheme(
                primary = theme.primary,
                onPrimary = theme.primaryForeground,
                primaryContainer = theme.primary.copy(alpha = 0.3f),
                onPrimaryContainer = theme.primaryForeground,
                secondary = theme.secondary,
                onSecondary = theme.secondaryForeground,
                secondaryContainer = theme.secondary.copy(alpha = 0.3f),
                onSecondaryContainer = theme.secondaryForeground,
                tertiary = theme.accent,
                onTertiary = theme.accentForeground,
                tertiaryContainer = theme.accent.copy(alpha = 0.3f),
                onTertiaryContainer = theme.accentForeground,
                error = theme.destructive,
                onError = theme.destructiveForeground,
                errorContainer = theme.destructive.copy(alpha = 0.3f),
                onErrorContainer = theme.destructiveForeground,
                background = theme.background,
                onBackground = theme.foreground,
                surface = theme.card,
                onSurface = theme.cardForeground,
                surfaceVariant = theme.muted,
                onSurfaceVariant = theme.mutedForeground,
                outline = theme.border,
                outlineVariant = theme.border.copy(alpha = 0.5f),
                scrim = Color.Black.copy(alpha = 0.5f),
                inverseSurface = theme.foreground,
                inverseOnSurface = theme.background,
                inversePrimary = theme.primaryForeground,
                surfaceTint = theme.primary.copy(alpha = 0.08f)
            )
        } else {
            lightColorScheme(
                primary = theme.primary,
                onPrimary = theme.primaryForeground,
                primaryContainer = theme.primary.copy(alpha = 0.15f),
                onPrimaryContainer = theme.primary,
                secondary = theme.secondary,
                onSecondary = theme.secondaryForeground,
                secondaryContainer = theme.secondary.copy(alpha = 0.15f),
                onSecondaryContainer = theme.secondary,
                tertiary = theme.accent,
                onTertiary = theme.accentForeground,
                tertiaryContainer = theme.accent.copy(alpha = 0.15f),
                onTertiaryContainer = theme.accent,
                error = theme.destructive,
                onError = theme.destructiveForeground,
                errorContainer = theme.destructive.copy(alpha = 0.1f),
                onErrorContainer = theme.destructive,
                background = theme.background,
                onBackground = theme.foreground,
                surface = theme.card,
                onSurface = theme.cardForeground,
                surfaceVariant = theme.muted,
                onSurfaceVariant = theme.mutedForeground,
                outline = theme.border,
                outlineVariant = theme.border.copy(alpha = 0.5f),
                scrim = Color.Black.copy(alpha = 0.5f),
                inverseSurface = theme.foreground,
                inverseOnSurface = theme.background,
                inversePrimary = theme.primaryForeground,
                surfaceTint = theme.primary.copy(alpha = 0.08f)
            )
        }
    }
    
    fun createShapes(theme: ShadcnTheme): Shapes {
        return Shapes(
            extraSmall = RoundedCornerShape(4.dp),
            small = RoundedCornerShape(theme.radiusSmall),
            medium = RoundedCornerShape(theme.radiusMedium),
            large = RoundedCornerShape(theme.radiusLarge),
            extraLarge = RoundedCornerShape(theme.radiusLarge * 1.5f)
        )
    }
    
    fun createTypography(): Typography {
        val bodyFontFamily = FontFamily.Default
        val displayFontFamily = FontFamily.Default
        
        return Typography(
            displayLarge = TextStyle(
                fontFamily = displayFontFamily,
                fontWeight = FontWeight.W400,
                fontSize = 57.sp,
                lineHeight = 64.sp,
                letterSpacing = (-0.25).sp
            ),
            displayMedium = TextStyle(
                fontFamily = displayFontFamily,
                fontWeight = FontWeight.W400,
                fontSize = 45.sp,
                lineHeight = 52.sp,
                letterSpacing = 0.sp
            ),
            displaySmall = TextStyle(
                fontFamily = displayFontFamily,
                fontWeight = FontWeight.W400,
                fontSize = 36.sp,
                lineHeight = 44.sp,
                letterSpacing = 0.sp
            ),
            headlineLarge = TextStyle(
                fontFamily = displayFontFamily,
                fontWeight = FontWeight.W400,
                fontSize = 32.sp,
                lineHeight = 40.sp,
                letterSpacing = 0.sp
            ),
            headlineMedium = TextStyle(
                fontFamily = displayFontFamily,
                fontWeight = FontWeight.W400,
                fontSize = 28.sp,
                lineHeight = 36.sp,
                letterSpacing = 0.sp
            ),
            headlineSmall = TextStyle(
                fontFamily = displayFontFamily,
                fontWeight = FontWeight.W400,
                fontSize = 24.sp,
                lineHeight = 32.sp,
                letterSpacing = 0.sp
            ),
            titleLarge = TextStyle(
                fontFamily = displayFontFamily,
                fontWeight = FontWeight.W400,
                fontSize = 22.sp,
                lineHeight = 28.sp,
                letterSpacing = 0.sp
            ),
            titleMedium = TextStyle(
                fontFamily = displayFontFamily,
                fontWeight = FontWeight.Medium,
                fontSize = 16.sp,
                lineHeight = 24.sp,
                letterSpacing = 0.15.sp
            ),
            titleSmall = TextStyle(
                fontFamily = displayFontFamily,
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp,
                lineHeight = 20.sp,
                letterSpacing = 0.1.sp
            ),
            bodyLarge = TextStyle(
                fontFamily = bodyFontFamily,
                fontWeight = FontWeight.W400,
                fontSize = 16.sp,
                lineHeight = 24.sp,
                letterSpacing = 0.5.sp
            ),
            bodyMedium = TextStyle(
                fontFamily = bodyFontFamily,
                fontWeight = FontWeight.W400,
                fontSize = 14.sp,
                lineHeight = 20.sp,
                letterSpacing = 0.25.sp
            ),
            bodySmall = TextStyle(
                fontFamily = bodyFontFamily,
                fontWeight = FontWeight.W400,
                fontSize = 12.sp,
                lineHeight = 16.sp,
                letterSpacing = 0.4.sp
            ),
            labelLarge = TextStyle(
                fontFamily = bodyFontFamily,
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp,
                lineHeight = 20.sp,
                letterSpacing = 0.1.sp
            ),
            labelMedium = TextStyle(
                fontFamily = bodyFontFamily,
                fontWeight = FontWeight.Medium,
                fontSize = 12.sp,
                lineHeight = 16.sp,
                letterSpacing = 0.5.sp
            ),
            labelSmall = TextStyle(
                fontFamily = bodyFontFamily,
                fontWeight = FontWeight.Medium,
                fontSize = 11.sp,
                lineHeight = 16.sp,
                letterSpacing = 0.5.sp
            )
        )
    }
}

@Composable
fun ChatBotTheme(
    theme: ShadcnTheme? = null,
    darkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit
) {
    val shadcnTheme = theme ?: if (darkTheme) DefaultTheme.dark() else DefaultTheme.light()
    val colorScheme = ShadcnThemeBuilder.createColorScheme(shadcnTheme)
    val shapes = ShadcnThemeBuilder.createShapes(shadcnTheme)
    val typography = ShadcnThemeBuilder.createTypography()
    
    CompositionLocalProvider(LocalShadcnTheme provides shadcnTheme) {
        MaterialTheme(
            colorScheme = colorScheme,
            shapes = shapes,
            typography = typography,
            content = content
        )
    }
}