package com.chatbot.theme.tailwind

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

object Tailwind {
    val colors = TwColors
    val spacing = TwSpacing
    val typography = TwTypography
    val radius = TwRadius
    val shadows = TwShadows
    val breakpoints = TwBreakpoints
}

object TwSpacing {
    val s0 = 0.dp
    val s0_5 = 2.dp
    val s1 = 4.dp
    val s1_5 = 6.dp
    val s2 = 8.dp
    val s2_5 = 10.dp
    val s3 = 12.dp
    val s3_5 = 14.dp
    val s4 = 16.dp
    val s5 = 20.dp
    val s6 = 24.dp
    val s7 = 28.dp
    val s8 = 32.dp
    val s9 = 36.dp
    val s10 = 40.dp
    val s11 = 44.dp
    val s12 = 48.dp
    val s14 = 56.dp
    val s16 = 64.dp
    val s20 = 80.dp
    val s24 = 96.dp
    val s28 = 112.dp
    val s32 = 128.dp
    val s36 = 144.dp
    val s40 = 160.dp
    val s44 = 176.dp
    val s48 = 192.dp
    val s52 = 208.dp
    val s56 = 224.dp
    val s60 = 240.dp
    val s64 = 256.dp
    val s72 = 288.dp
    val s80 = 320.dp
    val s96 = 384.dp
}

object TwTypography {
    val textXs = 12.sp
    val textSm = 14.sp
    val textBase = 16.sp
    val textLg = 18.sp
    val textXl = 20.sp
    val text2xl = 24.sp
    val text3xl = 30.sp
    val text4xl = 36.sp
    val text5xl = 48.sp
    val text6xl = 60.sp
    val text7xl = 72.sp
    val text8xl = 96.sp
    val text9xl = 128.sp
}

object TwRadius {
    val none = RoundedCornerShape(0.dp)
    val sm = RoundedCornerShape(2.dp)
    val base = RoundedCornerShape(4.dp)
    val md = RoundedCornerShape(6.dp)
    val lg = RoundedCornerShape(8.dp)
    val xl = RoundedCornerShape(12.dp)
    val xl2 = RoundedCornerShape(16.dp)
    val xl3 = RoundedCornerShape(24.dp)
    val full = RoundedCornerShape(50)
}

object TwShadows {
    val sm = "0 1px 2px 0 rgb(0 0 0 / 0.05)"
    val base = "0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)"
    val md = "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)"
    val lg = "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)"
    val xl = "0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)"
    val xl2 = "0 25px 50px -12px rgb(0 0 0 / 0.25)"
}

object TwBreakpoints {
    val sm = 640.dp
    val md = 768.dp
    val lg = 1024.dp
    val xl = 1280.dp
    val xl2 = 1536.dp
}