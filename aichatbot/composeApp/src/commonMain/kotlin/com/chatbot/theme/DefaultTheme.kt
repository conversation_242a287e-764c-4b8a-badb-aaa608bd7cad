package com.chatbot.theme

import com.chatbot.theme.tailwind.TwColors

object DefaultTheme {
    fun light() = ShadcnTheme(
        background = TwColors.white,
        foreground = TwColors.neutral950,
        card = TwColors.white,
        cardForeground = TwColors.neutral950,
        popover = TwColors.white,
        popoverForeground = TwColors.neutral950,
        primary = TwColors.neutral900,
        primaryForeground = TwColors.neutral50,
        secondary = TwColors.neutral100,
        secondaryForeground = TwColors.neutral900,
        muted = TwColors.neutral100,
        mutedForeground = TwColors.neutral500,
        accent = TwColors.neutral100,
        accentForeground = TwColors.neutral900,
        destructive = TwColors.red600,
        destructiveForeground = TwColors.neutral50,
        border = TwColors.neutral200,
        input = TwColors.neutral200,
        ring = TwColors.neutral400,
        chart1 = TwColors.orange500,
        chart2 = TwColors.cyan500,
        chart3 = TwColors.blue500,
        chart4 = TwColors.lime500,
        chart5 = TwColors.yellow500,
        sidebar = TwColors.neutral50,
        sidebarForeground = TwColors.neutral950,
        sidebarPrimary = TwColors.neutral900,
        sidebarPrimaryForeground = TwColors.neutral50,
        sidebarAccent = TwColors.neutral100,
        sidebarAccentForeground = TwColors.neutral900,
        sidebarBorder = TwColors.neutral200,
        sidebarRing = TwColors.neutral400
    )
    
    fun dark() = ShadcnTheme(
        background = TwColors.neutral950,
        foreground = TwColors.neutral50,
        card = TwColors.neutral900,
        cardForeground = TwColors.neutral50,
        popover = TwColors.neutral900,
        popoverForeground = TwColors.neutral50,
        primary = TwColors.neutral200,
        primaryForeground = TwColors.neutral900,
        secondary = TwColors.neutral800,
        secondaryForeground = TwColors.neutral50,
        muted = TwColors.neutral800,
        mutedForeground = TwColors.neutral400,
        accent = TwColors.neutral800,
        accentForeground = TwColors.neutral50,
        destructive = TwColors.red500,
        destructiveForeground = TwColors.neutral50,
        border = TwColors.white.copy(alpha = 0.1f),
        input = TwColors.white.copy(alpha = 0.15f),
        ring = TwColors.neutral500,
        chart1 = TwColors.blue400,
        chart2 = TwColors.emerald400,
        chart3 = TwColors.yellow500,
        chart4 = TwColors.purple600,
        chart5 = TwColors.red500,
        sidebar = TwColors.neutral900,
        sidebarForeground = TwColors.neutral50,
        sidebarPrimary = TwColors.blue400,
        sidebarPrimaryForeground = TwColors.neutral50,
        sidebarAccent = TwColors.neutral800,
        sidebarAccentForeground = TwColors.neutral50,
        sidebarBorder = TwColors.white.copy(alpha = 0.1f),
        sidebarRing = TwColors.neutral500
    )
}

object BlueTheme {
    fun light() = ShadcnTheme(
        background = TwColors.white,
        foreground = TwColors.slate950,
        card = TwColors.white,
        cardForeground = TwColors.slate950,
        popover = TwColors.white,
        popoverForeground = TwColors.slate950,
        primary = TwColors.blue600,
        primaryForeground = TwColors.blue50,
        secondary = TwColors.zinc100,
        secondaryForeground = TwColors.zinc900,
        muted = TwColors.zinc100,
        mutedForeground = TwColors.zinc500,
        accent = TwColors.zinc100,
        accentForeground = TwColors.zinc900,
        destructive = TwColors.red600,
        destructiveForeground = TwColors.white,
        border = TwColors.zinc200,
        input = TwColors.zinc200,
        ring = TwColors.blue600,
        chart1 = TwColors.orange500,
        chart2 = TwColors.cyan500,
        chart3 = TwColors.blue500,
        chart4 = TwColors.lime500,
        chart5 = TwColors.yellow500,
        sidebar = TwColors.zinc50,
        sidebarForeground = TwColors.slate950,
        sidebarPrimary = TwColors.blue600,
        sidebarPrimaryForeground = TwColors.blue50,
        sidebarAccent = TwColors.zinc100,
        sidebarAccentForeground = TwColors.zinc900,
        sidebarBorder = TwColors.zinc200,
        sidebarRing = TwColors.blue600
    )
    
    fun dark() = ShadcnTheme(
        background = TwColors.slate950,
        foreground = TwColors.zinc50,
        card = TwColors.slate950,
        cardForeground = TwColors.zinc50,
        popover = TwColors.slate950,
        popoverForeground = TwColors.zinc50,
        primary = TwColors.blue600,
        primaryForeground = TwColors.blue50,
        secondary = TwColors.slate800,
        secondaryForeground = TwColors.zinc50,
        muted = TwColors.slate800,
        mutedForeground = TwColors.zinc400,
        accent = TwColors.slate800,
        accentForeground = TwColors.zinc50,
        destructive = TwColors.red500,
        destructiveForeground = TwColors.white,
        border = TwColors.slate800,
        input = TwColors.slate800,
        ring = TwColors.blue600,
        chart1 = TwColors.blue400,
        chart2 = TwColors.emerald400,
        chart3 = TwColors.yellow500,
        chart4 = TwColors.purple600,
        chart5 = TwColors.red500,
        sidebar = TwColors.slate900,
        sidebarForeground = TwColors.zinc50,
        sidebarPrimary = TwColors.blue600,
        sidebarPrimaryForeground = TwColors.blue50,
        sidebarAccent = TwColors.slate800,
        sidebarAccentForeground = TwColors.zinc50,
        sidebarBorder = TwColors.slate800,
        sidebarRing = TwColors.blue600
    )
}