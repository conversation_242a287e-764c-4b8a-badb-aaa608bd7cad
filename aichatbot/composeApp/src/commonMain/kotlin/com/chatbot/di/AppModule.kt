package com.chatbot.di

import com.chatbot.data.database.DatabaseService
import com.chatbot.data.database.DriverFactory
import com.chatbot.data.services.LlmDataService
import com.chatbot.data.services.ProviderIconService
import com.chatbot.ui.LlmProvider
import com.russhwolf.settings.Settings
import org.koin.dsl.module

fun appModule() = module {
    // Settings
    single { Settings() }

    // Database
    single { DatabaseService(get<DriverFactory>().createDriver()) }

    // Services
    single { ProviderIconService(get()) }
    single { LlmDataService(get(), get()) }

    // ViewModels
    factory { LlmProvider(get()) }
}