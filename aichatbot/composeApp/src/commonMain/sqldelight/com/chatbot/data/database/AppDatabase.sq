-- LLM Provider table
CREATE TABLE llm_provider (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    provider_type TEXT NOT NULL,
    description TEXT,
    icon TEXT,
    api_key TEXT,
    base_url TEXT,
    proxy_url TEXT,
    api_version TEXT,
    timeout_seconds INTEGER NOT NULL DEFAULT 60,
    max_retries INTEGER NOT NULL DEFAULT 3,
    custom_headers TEXT,
    is_active INTEGER NOT NULL DEFAULT 1,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
);

-- LLM Model table
CREATE TABLE llm_model (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    provider_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    version TEXT,
    description TEXT,
    family TEXT,
    release_date TEXT,
    capabilities TEXT,
    input_tokens INTEGER,
    output_tokens INTEGER,
    total_tokens INTEGER,
    max_tokens INTEGER DEFAULT 128000,
    max_input_tokens INTEGER,
    max_output_tokens INTEGER,
    supported_languages TEXT,
    pricing_tier TEXT,
    pricing_input REAL,
    pricing_output REAL,
    currency TEXT DEFAULT 'USD',
    is_active INTEGER NOT NULL DEFAULT 1,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (provider_id) REFERENCES llm_provider(id) ON DELETE CASCADE
);

-- Queries for Provider
getAllProviders:
SELECT * FROM llm_provider ORDER BY name;

getProviderById:
SELECT * FROM llm_provider WHERE id = ?;

insertProvider:
INSERT INTO llm_provider(
    name, provider_type, description, icon, api_key, base_url, proxy_url, 
    api_version, timeout_seconds, max_retries, custom_headers, is_active, 
    created_at, updated_at
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

updateProvider:
UPDATE llm_provider SET
    name = ?,
    provider_type = ?,
    description = ?,
    icon = ?,
    api_key = ?,
    base_url = ?,
    proxy_url = ?,
    api_version = ?,
    timeout_seconds = ?,
    max_retries = ?,
    custom_headers = ?,
    is_active = ?,
    updated_at = ?
WHERE id = ?;

deleteProvider:
DELETE FROM llm_provider WHERE id = ?;

getLastInsertedProviderId:
SELECT last_insert_rowid();

-- Queries for Model
getAllModels:
SELECT * FROM llm_model ORDER BY name;

getModelsByProviderId:
SELECT * FROM llm_model WHERE provider_id = ? ORDER BY name;

getModelById:
SELECT * FROM llm_model WHERE id = ?;

insertModel:
INSERT INTO llm_model(
    provider_id, name, version, description, family, release_date, capabilities,
    input_tokens, output_tokens, total_tokens, max_tokens, max_input_tokens,
    max_output_tokens, supported_languages, pricing_tier, pricing_input,
    pricing_output, currency, is_active, created_at, updated_at
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

updateModel:
UPDATE llm_model SET
    provider_id = ?,
    name = ?,
    version = ?,
    description = ?,
    family = ?,
    release_date = ?,
    capabilities = ?,
    input_tokens = ?,
    output_tokens = ?,
    total_tokens = ?,
    max_tokens = ?,
    max_input_tokens = ?,
    max_output_tokens = ?,
    supported_languages = ?,
    pricing_tier = ?,
    pricing_input = ?,
    pricing_output = ?,
    currency = ?,
    is_active = ?,
    updated_at = ?
WHERE id = ?;

deleteModel:
DELETE FROM llm_model WHERE id = ?;

deleteModelsByProviderId:
DELETE FROM llm_model WHERE provider_id = ?;

getLastInsertedModelId:
SELECT last_insert_rowid();