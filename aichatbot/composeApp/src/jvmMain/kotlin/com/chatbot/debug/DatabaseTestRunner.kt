package com.chatbot.debug

import com.chatbot.data.database.DatabaseService
import com.chatbot.data.database.DriverFactory
import com.chatbot.data.models.LlmProviderModel
import com.chatbot.data.models.LlmModel
import com.chatbot.data.models.ModelCapability
import kotlinx.datetime.Clock
import kotlinx.coroutines.runBlocking

/**
 * 数据库测试运行器 - 用于调试数据库插入操作
 */
object DatabaseTestRunner {
    
    fun main() {
        runBlocking {
            println("🚀 开始数据库测试...")
            
            try {
                // 创建数据库服务
                val driverFactory = DriverFactory()
                val driver = driverFactory.createDriver()
                val databaseService = DatabaseService(driver)
                
                println("✅ 数据库连接成功")
                
                // 测试提供商插入
                testProviderInsert(databaseService)
                
                // 测试模型插入
                testModelInsert(databaseService)
                
                // 测试查询操作
                testQueries(databaseService)
                
                println("🎉 所有测试完成！")
                
            } catch (e: Exception) {
                println("❌ 测试失败: ${e.message}")
                e.printStackTrace()
            }
        }
    }
    
    private suspend fun testProviderInsert(databaseService: DatabaseService) {
        println("\n📝 测试提供商插入...")
        
        val testProvider = LlmProviderModel(
            id = null,
            name = "Test OpenAI",
            providerType = "openai",
            description = "测试用的OpenAI提供商",
            icon = "openai-icon.png",
            apiKey = "sk-test-key-12345",
            baseUrl = "https://api.openai.com/v1",
            proxyUrl = null,
            apiVersion = "v1",
            timeoutSeconds = 60,
            maxRetries = 3,
            customHeaders = mapOf("User-Agent" to "AIChatbot/1.0"),
            isActive = true,
            createdAt = Clock.System.now().toEpochMilliseconds(),
            updatedAt = Clock.System.now().toEpochMilliseconds()
        )
        
        val insertedProvider = databaseService.insertProvider(testProvider)
        println("✅ 插入提供商成功: ID=${insertedProvider.id}, Name=${insertedProvider.name}")
        
        // 验证插入的数据
        val retrievedProvider = databaseService.getProviderById(insertedProvider.id!!)
        if (retrievedProvider != null) {
            println("✅ 提供商数据验证成功")
            println("   - ID: ${retrievedProvider.id}")
            println("   - 名称: ${retrievedProvider.name}")
            println("   - 类型: ${retrievedProvider.providerType}")
            println("   - API密钥: ${retrievedProvider.apiKey?.take(10)}...")
            println("   - 基础URL: ${retrievedProvider.baseUrl}")
            println("   - 是否活跃: ${retrievedProvider.isActive}")
        } else {
            println("❌ 提供商数据验证失败")
        }
    }
    
    private suspend fun testModelInsert(databaseService: DatabaseService) {
        println("\n📝 测试模型插入...")
        
        // 首先获取一个提供商ID
        val providers = databaseService.getAllProviders()
        if (providers.isEmpty()) {
            println("❌ 没有找到提供商，无法测试模型插入")
            return
        }
        
        val providerId = providers.first().id!!
        
        val testModel = LlmModel(
            id = null,
            providerId = providerId,
            name = "gpt-4-turbo",
            version = "2024-01-25",
            description = "GPT-4 Turbo模型，支持128K上下文",
            family = "GPT-4",
            releaseDate = "2024-01-25",
            capabilities = setOf(
                ModelCapability.CHAT,
                ModelCapability.COMPLETION,
                ModelCapability.FUNCTION_CALLING,
                ModelCapability.VISION,
                ModelCapability.JSON_MODE
            ),
            inputTokens = 100000,
            outputTokens = 4000,
            totalTokens = 104000,
            maxTokens = 128000,
            maxInputTokens = 124000,
            maxOutputTokens = 4000,
            supportedLanguages = setOf("zh", "en", "ja", "ko", "fr", "de", "es"),
            pricingTier = "STANDARD",
            pricingInput = 0.01,
            pricingOutput = 0.03,
            currency = "USD",
            isActive = true,
            createdAt = Clock.System.now().toEpochMilliseconds(),
            updatedAt = Clock.System.now().toEpochMilliseconds()
        )
        
        val insertedModel = databaseService.insertModel(testModel)
        println("✅ 插入模型成功: ID=${insertedModel.id}, Name=${insertedModel.name}")
        
        // 验证插入的数据
        val modelsForProvider = databaseService.getModelsByProviderId(providerId)
        val retrievedModel = modelsForProvider.find { it.id == insertedModel.id }
        
        if (retrievedModel != null) {
            println("✅ 模型数据验证成功")
            println("   - ID: ${retrievedModel.id}")
            println("   - 名称: ${retrievedModel.name}")
            println("   - 版本: ${retrievedModel.version}")
            println("   - 能力: ${retrievedModel.capabilities.joinToString(", ")}")
            println("   - 最大令牌数: ${retrievedModel.maxTokens}")
            println("   - 输入定价: $${retrievedModel.pricingInput}")
            println("   - 输出定价: $${retrievedModel.pricingOutput}")
            println("   - 支持语言: ${retrievedModel.supportedLanguages.joinToString(", ")}")
        } else {
            println("❌ 模型数据验证失败")
        }
    }
    
    private suspend fun testQueries(databaseService: DatabaseService) {
        println("\n📝 测试查询操作...")
        
        // 测试获取所有提供商
        val allProviders = databaseService.getAllProviders()
        println("✅ 查询所有提供商: 共 ${allProviders.size} 个")
        allProviders.forEach { provider ->
            println("   - ${provider.name} (${provider.providerType}) - 活跃: ${provider.isActive}")
        }
        
        // 测试获取所有模型
        val allModels = databaseService.getAllModels()
        println("✅ 查询所有模型: 共 ${allModels.size} 个")
        allModels.forEach { model ->
            println("   - ${model.name} (Provider: ${model.providerId}) - 活跃: ${model.isActive}")
        }
        
        // 测试外键关系
        if (allProviders.isNotEmpty() && allModels.isNotEmpty()) {
            val firstProvider = allProviders.first()
            val modelsForProvider = databaseService.getModelsByProviderId(firstProvider.id!!)
            println("✅ 外键关系验证: 提供商 '${firstProvider.name}' 有 ${modelsForProvider.size} 个模型")
        }
    }
}

// 运行测试的主函数
fun main() {
    DatabaseTestRunner.main()
}