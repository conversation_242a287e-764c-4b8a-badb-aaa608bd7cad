package com.chatbot.data.database

import app.cash.sqldelight.db.SqlDriver
import app.cash.sqldelight.driver.jdbc.sqlite.JdbcSqliteDriver
import com.chatbot.data.database.AppDatabase
import java.io.File

actual class DriverFactory {
    actual fun createDriver(): SqlDriver {
        val databasePath = File(System.getProperty("user.home"), ".aichatbot/app.db")
        databasePath.parentFile.mkdirs()

        val driver = JdbcSqliteDriver("jdbc:sqlite:${databasePath.absolutePath}")
        try {
            AppDatabase.Schema.create(driver)
        } catch (e: Exception) {
            // Tables already exist, continue
        }
        return driver
    }
}